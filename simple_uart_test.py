#!/usr/bin/env python3
"""
简单的串口发送测试
测试MaixCam Pro的UART API
"""

from maix import uart, time

def test_simple_uart():
    """测试基本的串口发送功能"""
    print("开始串口测试...")
    
    try:
        # 初始化串口
        device = "/dev/ttyS0"
        serial = uart.UART(device, 115200)
        print(f"串口 {device} 初始化成功")
        
        # 测试1: 发送字符串
        print("\n=== 测试1: 发送字符串 ===")
        test_str = "Hello STM32!"
        serial.write_str(test_str)
        print(f"发送字符串: {test_str}")
        time.sleep_ms(1000)
        
        # 测试2: 发送bytes数据
        print("\n=== 测试2: 发送bytes数据 ===")
        test_bytes = b'\xFF\xFE\x01\xA0\x00\x78\x00\xFD\xFC'
        serial.write(test_bytes)
        hex_data = ' '.join(f'{b:02X}' for b in test_bytes)
        print(f"发送bytes: {hex_data}")
        time.sleep_ms(1000)
        
        # 测试3: 构建并发送坐标数据包
        print("\n=== 测试3: 发送坐标数据包 ===")
        x, y = 160, 120
        packet = build_coordinate_packet(x, y)
        serial.write(packet)
        hex_data = ' '.join(f'{b:02X}' for b in packet)
        print(f"发送坐标({x}, {y}): {hex_data}")
        time.sleep_ms(1000)
        
        # 测试4: 连续发送多个数据包
        print("\n=== 测试4: 连续发送多个数据包 ===")
        coordinates = [(100, 80), (200, 150), (50, 200)]
        for i, (x, y) in enumerate(coordinates):
            packet = build_coordinate_packet(x, y)
            serial.write(packet)
            hex_data = ' '.join(f'{b:02X}' for b in packet)
            print(f"数据包{i+1} - 坐标({x}, {y}): {hex_data}")
            time.sleep_ms(500)
        
        print("\n串口测试完成！")
        
    except Exception as e:
        print(f"串口测试错误: {e}")

def build_coordinate_packet(x, y, data_type=0x01):
    """构建坐标数据包"""
    packet = bytearray()
    
    # 起始标志
    packet.extend([0xFF, 0xFE])
    
    # 数据类型 (0x01=矩形中心)
    packet.append(data_type)
    
    # X坐标 (2字节，小端序)
    packet.extend(x.to_bytes(2, 'little'))
    
    # Y坐标 (2字节，小端序)
    packet.extend(y.to_bytes(2, 'little'))
    
    # 结束标志
    packet.extend([0xFD, 0xFC])
    
    # 转换为bytes类型
    return bytes(packet)

def main():
    """主函数"""
    try:
        test_simple_uart()
    except KeyboardInterrupt:
        print("\n测试中断")
    except Exception as e:
        print(f"程序错误: {e}")

if __name__ == "__main__":
    main()
