/*
 * STM32端接收MaixCam Pro数据的示例代码
 * 用于接收和解析坐标数据
 */

#include "main.h"
#include "usart.h"
#include <stdio.h>
#include <string.h>

// 数据包定义
#define PACKET_START1    0xFF
#define PACKET_START2    0xFE
#define PACKET_END1      0xFD
#define PACKET_END2      0xFC

#define DATA_TYPE_RECT   0x01
#define DATA_TYPE_LASER  0x02
#define DATA_TYPE_BOTH   0x03

// 接收缓冲区
#define RX_BUFFER_SIZE   32
uint8_t rx_buffer[RX_BUFFER_SIZE];
uint8_t rx_index = 0;

// 解析状态
typedef enum {
    WAIT_START1,
    WAIT_START2,
    WAIT_TYPE,
    WAIT_DATA,
    WAIT_END1,
    WAIT_END2
} ParseState_t;

ParseState_t parse_state = WAIT_START1;

// 坐标数据结构
typedef struct {
    uint8_t type;
    uint16_t x;
    uint16_t y;
    uint16_t laser_x;  // 仅在组合数据时有效
    uint16_t laser_y;  // 仅在组合数据时有效
    uint8_t valid;
} CoordinateData_t;

CoordinateData_t latest_data = {0};

/**
 * @brief 处理接收到的字节
 * @param byte 接收到的字节
 */
void ProcessReceivedByte(uint8_t byte)
{
    static uint8_t expected_length = 0;
    static uint8_t data_count = 0;
    
    switch (parse_state) {
        case WAIT_START1:
            if (byte == PACKET_START1) {
                rx_index = 0;
                rx_buffer[rx_index++] = byte;
                parse_state = WAIT_START2;
            }
            break;
            
        case WAIT_START2:
            if (byte == PACKET_START2) {
                rx_buffer[rx_index++] = byte;
                parse_state = WAIT_TYPE;
            } else {
                parse_state = WAIT_START1;
            }
            break;
            
        case WAIT_TYPE:
            rx_buffer[rx_index++] = byte;
            
            // 根据数据类型确定期望的数据长度
            if (byte == DATA_TYPE_RECT || byte == DATA_TYPE_LASER) {
                expected_length = 4;  // X(2) + Y(2)
            } else if (byte == DATA_TYPE_BOTH) {
                expected_length = 8;  // X(2) + Y(2) + LaserX(2) + LaserY(2)
            } else {
                parse_state = WAIT_START1;  // 未知类型，重新开始
                break;
            }
            
            data_count = 0;
            parse_state = WAIT_DATA;
            break;
            
        case WAIT_DATA:
            rx_buffer[rx_index++] = byte;
            data_count++;
            
            if (data_count >= expected_length) {
                parse_state = WAIT_END1;
            }
            break;
            
        case WAIT_END1:
            if (byte == PACKET_END1) {
                rx_buffer[rx_index++] = byte;
                parse_state = WAIT_END2;
            } else {
                parse_state = WAIT_START1;
            }
            break;
            
        case WAIT_END2:
            if (byte == PACKET_END2) {
                rx_buffer[rx_index++] = byte;
                // 数据包接收完成，解析数据
                ParsePacket();
            }
            parse_state = WAIT_START1;
            break;
    }
}

/**
 * @brief 解析完整的数据包
 */
void ParsePacket(void)
{
    if (rx_index < 7) {  // 最小数据包长度
        return;
    }
    
    // 清除之前的数据
    memset(&latest_data, 0, sizeof(latest_data));
    
    // 解析数据类型
    latest_data.type = rx_buffer[2];
    
    // 解析坐标 (小端序)
    latest_data.x = (uint16_t)(rx_buffer[3] | (rx_buffer[4] << 8));
    latest_data.y = (uint16_t)(rx_buffer[5] | (rx_buffer[6] << 8));
    
    // 如果是组合数据，解析激光点坐标
    if (latest_data.type == DATA_TYPE_BOTH && rx_index >= 11) {
        latest_data.laser_x = (uint16_t)(rx_buffer[7] | (rx_buffer[8] << 8));
        latest_data.laser_y = (uint16_t)(rx_buffer[9] | (rx_buffer[10] << 8));
    }
    
    latest_data.valid = 1;
    
    // 处理接收到的数据
    HandleCoordinateData(&latest_data);
}

/**
 * @brief 处理坐标数据
 * @param data 坐标数据指针
 */
void HandleCoordinateData(CoordinateData_t *data)
{
    char msg[100];
    
    switch (data->type) {
        case DATA_TYPE_RECT:
            sprintf(msg, "矩形中心: (%d, %d)\r\n", data->x, data->y);
            HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 100);
            
            // 在这里添加您的矩形处理逻辑
            // 例如：控制舵机指向矩形中心
            break;
            
        case DATA_TYPE_LASER:
            sprintf(msg, "激光点: (%d, %d)\r\n", data->x, data->y);
            HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 100);
            
            // 在这里添加您的激光点处理逻辑
            break;
            
        case DATA_TYPE_BOTH:
            sprintf(msg, "组合数据 - 矩形: (%d, %d), 激光: (%d, %d)\r\n", 
                    data->x, data->y, data->laser_x, data->laser_y);
            HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 100);
            
            // 在这里添加您的组合数据处理逻辑
            // 例如：计算激光点相对于矩形的偏移量
            int16_t offset_x = data->laser_x - data->x;
            int16_t offset_y = data->laser_y - data->y;
            sprintf(msg, "偏移量: (%d, %d)\r\n", offset_x, offset_y);
            HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 100);
            break;
            
        default:
            sprintf(msg, "未知数据类型: 0x%02X\r\n", data->type);
            HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 100);
            break;
    }
}

/**
 * @brief 串口接收中断回调函数
 * @param huart 串口句柄
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART2) {  // 假设使用USART2接收数据
        uint8_t received_byte;
        HAL_UART_Receive(huart, &received_byte, 1, 0);
        ProcessReceivedByte(received_byte);
        
        // 重新启动接收
        HAL_UART_Receive_IT(huart, &received_byte, 1);
    }
}

/**
 * @brief 获取最新的坐标数据
 * @return 坐标数据指针
 */
CoordinateData_t* GetLatestCoordinateData(void)
{
    if (latest_data.valid) {
        return &latest_data;
    }
    return NULL;
}
