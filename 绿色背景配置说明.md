# 绿色背景矩形识别配置说明

## 程序功能

这个程序专门用于检测**绿色背景**上的黑色边框矩形，功能与原程序相同，但针对绿色背景进行了优化。

## 检测流程

1. **第一步**：检测绿色背景区域
2. **第二步**：在绿色区域内检测黑色边框
3. **第三步**：识别矩形并提取角点坐标
4. **第四步**：通过串口发送坐标数据

## 绿色阈值配置

### 当前默认设置
```python
green_threshold = [[30, 80, -40, -10, -20, 20]]
```

### 不同绿色背景的推荐设置

#### 1. 鲜绿色背景（如绿色纸张）
```python
green_threshold = [[25, 75, -45, -15, -25, 15]]
```

#### 2. 深绿色背景（如深绿色布料）
```python
green_threshold = [[15, 60, -50, -20, -30, 10]]
```

#### 3. 浅绿色背景（如浅绿色墙面）
```python
green_threshold = [[40, 90, -35, -5, -15, 25]]
```

#### 4. 黄绿色背景（偏黄的绿色）
```python
green_threshold = [[35, 85, -30, -5, 0, 40]]
```

#### 5. 蓝绿色背景（偏蓝的绿色）
```python
green_threshold = [[30, 80, -45, -15, -40, 0]]
```

## LAB色彩空间绿色特征

### L通道（亮度）
- **浅绿色**: 50-90
- **中等绿色**: 30-70  
- **深绿色**: 10-50

### A通道（绿-红轴）
- **纯绿色**: -50到-20
- **偏黄绿色**: -30到-5
- **偏蓝绿色**: -45到-25

### B通道（蓝-黄轴）
- **偏黄绿色**: 10到40
- **中性绿色**: -20到20
- **偏蓝绿色**: -40到0

## 调试模式

程序提供调试模式来帮助您调整绿色阈值：

```python
DEBUG_MODE = True  # 设置为True启用调试模式
```

调试模式下：
- 只显示检测到的绿色区域
- 用黄色框标出绿色区域
- 在控制台输出绿色区域信息
- 不进行矩形检测

## 参数调整指南

### 如果检测不到绿色区域
1. **增大L通道范围**：`[20, 90, -40, -10, -20, 20]`
2. **放宽A通道范围**：`[30, 80, -50, 0, -20, 20]`
3. **降低像素阈值**：`pixels_threshold=150`

### 如果误检测太多
1. **缩小L通道范围**：`[35, 75, -40, -10, -20, 20]`
2. **收紧A通道范围**：`[30, 80, -35, -15, -20, 20]`
3. **提高像素阈值**：`pixels_threshold=400`

### 如果绿色区域不稳定
1. **启用合并模式**：`merge=True`
2. **增加面积阈值**：`area_threshold=500`
3. **使用形态学操作**（需要额外代码）

## 光照条件影响

### 强光环境
- L通道值会增大，需要调高上限
- 建议：`[40, 95, -40, -10, -20, 20]`

### 弱光环境  
- L通道值会减小，需要调低下限
- 建议：`[15, 70, -40, -10, -20, 20]`

### 人工光源
- 可能影响B通道，需要放宽B通道范围
- 建议：`[30, 80, -40, -10, -30, 30]`

## 使用建议

1. **首次使用**：
   - 先启用DEBUG_MODE调试绿色阈值
   - 确认能稳定检测绿色区域后再关闭调试模式

2. **环境适配**：
   - 根据实际绿色背景调整阈值参数
   - 在不同光照条件下测试效果

3. **性能优化**：
   - 绿色区域检测的像素阈值不要设置太低
   - 合理设置宽度阈值范围

## 故障排除

### 问题1：检测不到绿色背景
**解决方案**：
- 检查绿色背景是否足够大
- 调整绿色阈值参数
- 确认光照条件合适

### 问题2：绿色区域检测不稳定
**解决方案**：
- 启用merge=True合并相邻区域
- 增加面积和像素阈值
- 使用更均匀的绿色背景

### 问题3：矩形检测精度低
**解决方案**：
- 确保黑色边框清晰
- 调整矩形检测的threshold参数
- 检查绿色背景与黑色边框的对比度

## 数据格式

串口输出数据格式与原程序相同：
```
[0x13, 0x23, center_x, center_y, top_left_x, top_left_y, top_right_x, top_right_y, bottom_right_x, bottom_right_y, bottom_left_x, bottom_left_y]
```

## 显示信息

- **左上角**：FPS显示（绿色）
- **右上角**：模式标识"Green BG Mode"（青色）
- **绿色区域**：蓝色边框标识
- **矩形中心**：红色十字标记
- **矩形角点**：白色圆点标记
