#!/usr/bin/env python3
"""
MaixCAM Pro 黑色胶带边框识别程序（原生库版本）
功能：使用MaixPy原生库识别黑色胶带边框，显示在胶带中心线
特性：高性能、低延迟、原生优化
作者：AI Assistant
"""

from maix import camera, display, image, app, time
import math

class NativeRectangleDetector:
    def __init__(self):
        """初始化检测器"""
        # 初始化摄像头和显示器
        self.cam = camera.Camera(320, 240, image.Format.FMT_RGB888)
        self.disp = display.Display()
        
        # 检测参数（高帧率优化）
        self.min_area = 1000        # 提高最小面积，减少小目标处理
        self.max_area = 12000       # 适中的最大面积
        self.min_aspect_ratio = 0.4 # 收紧宽高比范围，减少计算
        self.max_aspect_ratio = 3.0 # 收紧宽高比范围

        # 白色检测阈值（高帧率优化 - 减少阈值数量）
        self.white_thresholds = [
            [70, 100, -128, 127, -128, 127],  # 主要白色区域
            [60, 95, -128, 127, -128, 127],   # 次要白色区域
        ]

        # 黑色检测阈值（高帧率优化 - 减少阈值数量）
        self.black_thresholds = [
            [0, 40, -128, 127, -128, 127],   # 主要暗区域
            [0, 55, -128, 127, -128, 127],   # 包含反光的暗区域
        ]

        # 红色激光笔检测阈值（LAB色彩空间）
        self.red_laser_thresholds = [
            [50, 100, 20, 127, -128, 127],   # 很亮的红色激光点
            [40, 100, 15, 127, -128, 127],   # 中等亮度红色激光点
            [30, 95, 10, 127, -128, 127],    # 较暗红色激光点
        ]
        
        # 高帧率性能优化
        self.process_every_n_frames = 2  # 每2帧检测一次，提升帧率
        self.frame_count = 0
        self.last_rectangles = []

        # 帧率计算（优化）
        self.fps_counter = 0
        self.fps_start_time = time.ticks_ms()
        self.current_fps = 0.0
        self.fps_update_interval = 500  # 更频繁更新FPS显示

        # 胶带参数
        self.tape_width_pixels = 12  # 恢复标准宽度

        # 高帧率模式参数
        self.high_fps_mode = True
        self.merge_margin_white = 2   # 减小合并边距，提升速度
        self.merge_margin_black = 6   # 减小合并边距
        self.max_detections = 2       # 限制最大检测数量

        # 简化显示模式
        self.simple_display = True    # 简化显示内容
        self.show_detailed_info = False  # 关闭详细信息显示

        # 激光笔检测参数（针对过亮激光优化）
        self.laser_detection_ready = True  # 为激光笔检测优化
        self.enable_laser_detection = True  # 启用激光笔检测
        self.laser_min_area = 10           # 激光点最小面积（提高以过滤光晕）
        self.laser_max_area = 150          # 激光点最大面积（降低以避免大光晕）
        self.laser_circle_radius = 12      # 激光点周围圆圈半径（稍小）
        self.last_laser_points = []        # 缓存激光点位置

        # 过亮激光处理参数
        self.bright_laser_mode = True      # 启用过亮激光处理
        self.min_density_threshold = 0.7   # 提高密度要求，过滤光晕
        self.max_aspect_ratio = 2.0        # 更严格的宽高比要求
        self.center_weight_threshold = 0.8 # 中心权重阈值
        
    def update_fps(self):
        """更新帧率（优化版）"""
        self.fps_counter += 1
        current_time = time.ticks_ms()

        if current_time - self.fps_start_time >= self.fps_update_interval:
            elapsed_time = (current_time - self.fps_start_time) / 1000.0
            self.current_fps = self.fps_counter / elapsed_time
            self.fps_counter = 0
            self.fps_start_time = current_time
    
    def find_white_rectangles(self, img):
        """
        使用MaixPy原生函数查找白色矩形（A4纸）
        Args:
            img: maix.image.Image对象
        Returns:
            rectangles: 检测到的矩形列表
        """
        rectangles = []

        # 使用多个阈值检测白色区域（A4纸）
        for threshold in self.white_thresholds:
            try:
                # 查找白色色块
                blobs = img.find_blobs(
                    thresholds=[threshold],
                    area_threshold=self.min_area,
                    pixels_threshold=self.min_area,
                    merge=True,
                    margin=self.merge_margin_white
                )

                for blob in blobs:
                    # 检查面积
                    if blob.area() < self.min_area or blob.area() > self.max_area:
                        continue

                    # 检查宽高比
                    w = blob.w()
                    h = blob.h()
                    if h == 0:
                        continue

                    aspect_ratio = w / h
                    if aspect_ratio < self.min_aspect_ratio or aspect_ratio > self.max_aspect_ratio:
                        continue

                    # 检查密度 - 白色区域应该有较高的密度（放宽要求）
                    density = blob.density()
                    if density >= 0.4:  # 降低密度要求，适应光照变化
                        rectangles.append({
                            'blob': blob,
                            'x': blob.x(),
                            'y': blob.y(),
                            'w': w,
                            'h': h,
                            'area': blob.area(),
                            'aspect_ratio': aspect_ratio,
                            'density': density,
                            'type': 'white'  # 标记为白色检测
                        })

            except Exception as e:
                print(f"白色检测过程出错: {e}")
                continue

        return rectangles

    def find_black_rectangles(self, img):
        """
        使用MaixPy原生函数查找黑色边线（胶带边框）
        Args:
            img: maix.image.Image对象
        Returns:
            rectangles: 检测到的矩形列表
        """
        rectangles = []

        # 使用多个阈值检测黑色区域（胶带边线）
        for threshold in self.black_thresholds:
            try:
                # 查找黑色色块（反光优化）
                blobs = img.find_blobs(
                    thresholds=[threshold],
                    area_threshold=self.min_area // 2,  # 降低面积要求，因为反光可能导致断开
                    pixels_threshold=self.min_area // 2,
                    merge=True,
                    margin=self.merge_margin_black  # 大margin连接反光断开的胶带
                )

                for blob in blobs:
                    # 检查面积
                    if blob.area() < self.min_area or blob.area() > self.max_area:
                        continue

                    # 检查宽高比
                    w = blob.w()
                    h = blob.h()
                    if h == 0:
                        continue

                    aspect_ratio = w / h
                    if aspect_ratio < self.min_aspect_ratio or aspect_ratio > self.max_aspect_ratio:
                        continue

                    # 检查密度 - 边框密度范围（考虑反光影响）
                    density = blob.density()
                    if 0.05 <= density <= 0.8:  # 放宽密度范围，适应反光情况
                        rectangles.append({
                            'blob': blob,
                            'x': blob.x(),
                            'y': blob.y(),
                            'w': w,
                            'h': h,
                            'area': blob.area(),
                            'aspect_ratio': aspect_ratio,
                            'density': density,
                            'type': 'black'  # 标记为黑色检测
                        })

            except Exception as e:
                print(f"黑色检测过程出错: {e}")
                continue

        return rectangles

    def find_all_rectangles(self, img):
        """
        同时检测白色A4纸和黑色胶带边框（反光优化版）
        Args:
            img: maix.image.Image对象
        Returns:
            rectangles: 检测到的矩形列表
        """
        # 检测白色A4纸
        white_rects = self.find_white_rectangles(img)

        # 检测黑色胶带边框
        black_rects = self.find_black_rectangles(img)

        # 高帧率模式：简化反光检测
        if len(black_rects) == 0 and not self.high_fps_mode:
            black_rects = self.find_black_rectangles_relaxed(img)

        # 合并结果
        all_rects = white_rects + black_rects

        # 去重和筛选最佳结果
        return self.filter_best_rectangles(all_rects)

    def find_black_rectangles_relaxed(self, img):
        """
        使用更宽松的参数检测黑色胶带（针对强反光情况）
        """
        rectangles = []

        # 更宽松的黑色阈值（适应强反光）
        relaxed_thresholds = [
            [0, 65, -128, 127, -128, 127],   # 包含更多反光区域
            [0, 75, -128, 127, -128, 127],   # 非常宽松的阈值
        ]

        for threshold in relaxed_thresholds:
            try:
                blobs = img.find_blobs(
                    thresholds=[threshold],
                    area_threshold=self.min_area // 3,  # 更低的面积要求
                    pixels_threshold=self.min_area // 3,
                    merge=True,
                    margin=15  # 更大的合并边距
                )

                for blob in blobs:
                    if blob.area() < self.min_area // 2:
                        continue

                    w = blob.w()
                    h = blob.h()
                    if h == 0:
                        continue

                    aspect_ratio = w / h
                    if aspect_ratio < 0.2 or aspect_ratio > 5.0:  # 更宽松的宽高比
                        continue

                    density = blob.density()
                    if 0.02 <= density <= 0.9:  # 非常宽松的密度范围
                        rectangles.append({
                            'blob': blob,
                            'x': blob.x(),
                            'y': blob.y(),
                            'w': w,
                            'h': h,
                            'area': blob.area(),
                            'aspect_ratio': aspect_ratio,
                            'density': density,
                            'type': 'black_relaxed'  # 标记为宽松检测
                        })

            except Exception as e:
                print(f"宽松黑色检测出错: {e}")
                continue

        return rectangles

    def find_red_laser_points(self, img):
        """
        检测红色激光笔照射点
        Args:
            img: maix.image.Image对象
        Returns:
            laser_points: 检测到的激光点列表
        """
        laser_points = []

        if not self.enable_laser_detection:
            return laser_points

        # 使用多个阈值检测红色激光点
        for threshold in self.red_laser_thresholds:
            try:
                # 查找红色激光点
                blobs = img.find_blobs(
                    thresholds=[threshold],
                    area_threshold=self.laser_min_area,
                    pixels_threshold=self.laser_min_area,
                    merge=True,
                    margin=2  # 小margin，激光点通常很集中
                )

                for blob in blobs:
                    # 检查面积 - 激光点应该很小
                    if blob.area() < self.laser_min_area or blob.area() > self.laser_max_area:
                        continue

                    # 检查形状 - 激光点应该接近圆形
                    w = blob.w()
                    h = blob.h()
                    if w == 0 or h == 0:
                        continue

                    # 宽高比应该接近1（圆形）- 更严格要求
                    aspect_ratio = max(w, h) / min(w, h)
                    if aspect_ratio > self.max_aspect_ratio:  # 过亮激光容易变形
                        continue

                    # 检查密度 - 过亮激光需要更高密度要求
                    density = blob.density()
                    if density < self.min_density_threshold:  # 提高密度要求过滤光晕
                        continue

                    # 过亮激光特殊处理：检查中心集中度
                    if self.bright_laser_mode and not self.is_concentrated_laser_point(blob):
                        continue

                    # 计算激光点中心
                    center_x = blob.cx()
                    center_y = blob.cy()

                    laser_points.append({
                        'blob': blob,
                        'x': blob.x(),
                        'y': blob.y(),
                        'w': w,
                        'h': h,
                        'center_x': center_x,
                        'center_y': center_y,
                        'area': blob.area(),
                        'density': density,
                        'aspect_ratio': aspect_ratio
                    })

            except Exception as e:
                print(f"红色激光检测出错: {e}")
                continue

        # 去重和筛选最佳激光点
        return self.filter_laser_points(laser_points)

    def filter_laser_points(self, laser_points):
        """筛选最佳激光点"""
        if not laser_points:
            return []

        # 按密度排序，选择最亮的点
        laser_points.sort(key=lambda p: p['density'], reverse=True)

        # 去除距离太近的点（可能是同一个激光点的重复检测）
        filtered = []
        for point in laser_points:
            is_duplicate = False
            for existing in filtered:
                # 计算距离
                dx = point['center_x'] - existing['center_x']
                dy = point['center_y'] - existing['center_y']
                distance = (dx*dx + dy*dy)**0.5

                # 如果距离小于激光点半径，认为是重复
                if distance < self.laser_circle_radius:
                    is_duplicate = True
                    break

            if not is_duplicate:
                filtered.append(point)

            # 最多保留3个激光点
            if len(filtered) >= 3:
                break

        return filtered

    def is_concentrated_laser_point(self, blob):
        """
        检查是否为集中的激光点（而不是散开的光晕）
        Args:
            blob: 检测到的色块
        Returns:
            bool: 是否为集中的激光点
        """
        try:
            # 获取色块的边界框
            w, h = blob.w(), blob.h()

            # 计算中心区域（色块中心的50%区域）
            center_w = max(1, w // 2)
            center_h = max(1, h // 2)

            # 检查中心区域是否足够小且集中
            # 真正的激光点应该有一个很小很亮的中心
            if center_w > 8 or center_h > 8:  # 中心区域太大，可能是光晕
                return False

            # 检查宽高比是否接近正方形（激光点应该接近圆形）
            center_aspect = max(center_w, center_h) / max(1, min(center_w, center_h))
            if center_aspect > 1.8:  # 中心区域太长，不像激光点
                return False

            # 检查总面积是否合理
            total_area = blob.area()
            expected_area = 3.14159 * ((min(w, h) / 2) ** 2)  # 圆形面积估算
            area_ratio = total_area / max(1, expected_area)

            # 如果实际面积远大于圆形面积，可能是星状光晕
            if area_ratio > 1.5:  # 面积比圆形大50%以上，可能是光晕
                return False

            return True

        except Exception as e:
            print(f"激光点集中度检查出错: {e}")
            return True  # 出错时默认通过
    
    def filter_best_rectangles(self, rectangles):
        """筛选最佳矩形结果（高帧率优化）"""
        if not rectangles:
            return []

        # 快速按面积排序，只保留最大的几个
        rectangles.sort(key=lambda r: r['area'], reverse=True)

        # 限制处理数量，提升性能
        max_process = min(len(rectangles), 6)  # 最多处理6个
        rectangles = rectangles[:max_process]

        # 简化的去重逻辑
        filtered = []
        for rect in rectangles:
            is_duplicate = False
            for existing in filtered:
                # 简化重叠检查
                if self.simple_overlap_check(rect, existing):
                    is_duplicate = True
                    break

            if not is_duplicate:
                filtered.append(rect)

            # 限制最大检测数量
            if len(filtered) >= self.max_detections:
                break

        return filtered

    def simple_overlap_check(self, rect1, rect2):
        """简化的重叠检查（提升性能）"""
        x1, y1, w1, h1 = rect1['x'], rect1['y'], rect1['w'], rect1['h']
        x2, y2, w2, h2 = rect2['x'], rect2['y'], rect2['w'], rect2['h']

        # 简单的中心点距离检查
        center1_x, center1_y = x1 + w1//2, y1 + h1//2
        center2_x, center2_y = x2 + w2//2, y2 + h2//2

        distance = ((center1_x - center2_x)**2 + (center1_y - center2_y)**2)**0.5
        min_size = min(min(w1, h1), min(w2, h2))

        return distance < min_size * 0.8
    
    def rectangles_overlap(self, rect1, rect2):
        """检查两个矩形是否重叠"""
        x1, y1, w1, h1 = rect1['x'], rect1['y'], rect1['w'], rect1['h']
        x2, y2, w2, h2 = rect2['x'], rect2['y'], rect2['w'], rect2['h']
        
        # 计算重叠区域
        overlap_x = max(0, min(x1 + w1, x2 + w2) - max(x1, x2))
        overlap_y = max(0, min(y1 + h1, y2 + h2) - max(y1, y2))
        overlap_area = overlap_x * overlap_y
        
        # 如果重叠面积超过较小矩形的50%，认为是重叠
        min_area = min(rect1['area'], rect2['area'])
        return overlap_area > min_area * 0.5
    
    def calculate_tape_border_rectangle(self, rect):
        """计算胶带边框矩形（在白色A4纸外围）"""
        x, y, w, h = rect['x'], rect['y'], rect['w'], rect['h']

        # 向外扩展胶带宽度
        expand = self.tape_width_pixels // 2

        border_x = max(0, x - expand)
        border_y = max(0, y - expand)
        border_w = w + 2 * expand
        border_h = h + 2 * expand

        return {
            'x': int(border_x),
            'y': int(border_y),
            'w': int(border_w),
            'h': int(border_h)
        }
    
    def draw_rectangles(self, img, rectangles):
        """绘制检测结果（高帧率优化版）"""
        for i, rect in enumerate(rectangles):
            rect_type = rect.get('type', 'unknown')

            if rect_type == 'white':
                # 简化绘制 - 只绘制主要框架
                if self.simple_display:
                    # 只绘制胶带边框位置（蓝色）
                    tape_rect = self.calculate_tape_border_rectangle(rect)
                    img.draw_rect(
                        tape_rect['x'], tape_rect['y'],
                        tape_rect['w'], tape_rect['h'],
                        color=image.COLOR_BLUE, thickness=2
                    )
                else:
                    # 完整绘制
                    img.draw_rect(
                        rect['x'], rect['y'],
                        rect['w'], rect['h'],
                        color=image.COLOR_GREEN, thickness=1
                    )
                    tape_rect = self.calculate_tape_border_rectangle(rect)
                    img.draw_rect(
                        tape_rect['x'], tape_rect['y'],
                        tape_rect['w'], tape_rect['h'],
                        color=image.COLOR_BLUE, thickness=2
                    )

                # 简化标签
                if not self.simple_display:
                    label = f"A4-{i+1}"
                    img.draw_string(
                        rect['x'], rect['y'] - 12, label,
                        color=image.COLOR_GREEN, scale=0.8
                    )

            elif rect_type == 'black' or rect_type == 'black_relaxed':
                # 简化黑色胶带绘制
                color = image.COLOR_BLUE
                img.draw_rect(
                    rect['x'], rect['y'],
                    rect['w'], rect['h'],
                    color=color, thickness=2
                )

                # 简化标签
                if not self.simple_display:
                    label = f"T{i+1}"  # 简化标签
                    img.draw_string(
                        rect['x'], rect['y'] - 12, label,
                        color=color, scale=0.8
                    )

            # 只在非简化模式显示详细信息
            if self.show_detailed_info and not self.simple_display:
                ratio_text = f"{rect['aspect_ratio']:.1f}"
                img.draw_string(
                    rect['x'], rect['y'] + rect['h'] + 5, ratio_text,
                    color=image.COLOR_RED, scale=0.6
                )

    def calculate_inner_paper_rectangle(self, rect):
        """从胶带边框计算内部A4纸位置"""
        x, y, w, h = rect['x'], rect['y'], rect['w'], rect['h']

        # 向内缩进胶带宽度
        shrink = self.tape_width_pixels // 2

        paper_x = x + shrink
        paper_y = y + shrink
        paper_w = max(w - 2 * shrink, w * 0.7)
        paper_h = max(h - 2 * shrink, h * 0.7)

        return {
            'x': int(paper_x),
            'y': int(paper_y),
            'w': int(paper_w),
            'h': int(paper_h)
        }
    
    def draw_info(self, img, rect_count):
        """绘制信息（高帧率优化版）"""
        # 主要信息 - FPS
        fps_text = f"FPS: {self.current_fps:.1f}"
        img.draw_string(10, 10, fps_text, color=image.COLOR_GREEN, scale=1.0)

        if self.simple_display:
            # 简化模式 - 只显示核心信息
            if rect_count > 0:
                count_text = f"Detected: {rect_count}"
                img.draw_string(10, 30, count_text, color=image.COLOR_BLUE, scale=0.8)

            # 高帧率模式提示
            mode_text = "High-FPS Mode"
            img.draw_string(10, 50, mode_text, color=image.COLOR_PURPLE, scale=0.7)
        else:
            # 完整模式
            count_text = f"Objects: {rect_count}"
            img.draw_string(10, 30, count_text, color=image.COLOR_GREEN, scale=0.9)

            res_text = f"{img.width()}x{img.height()}"
            img.draw_string(10, 50, res_text, color=image.COLOR_YELLOW, scale=0.7)

            if rect_count == 0:
                tip_text = "No detection"
                img.draw_string(10, 70, tip_text, color=image.COLOR_YELLOW, scale=0.7)
            else:
                tip_text = "Blue = Tape"
                img.draw_string(10, 70, tip_text, color=image.COLOR_BLUE, scale=0.6)

    def draw_laser_points(self, img, laser_points):
        """绘制红色激光点（针对过亮激光优化）"""
        for i, point in enumerate(laser_points):
            center_x = point['center_x']
            center_y = point['center_y']

            # 根据激光亮度调整显示
            density = point['density']

            # 绘制激光点周围的圆圈（红色主题）
            if density > 0.9:  # 非常亮的激光
                # 使用更醒目的颜色组合
                img.draw_circle(
                    center_x, center_y, self.laser_circle_radius,
                    color=image.COLOR_YELLOW, thickness=2  # 黄色外圈
                )
                img.draw_circle(
                    center_x, center_y, self.laser_circle_radius - 3,
                    color=image.COLOR_RED, thickness=1     # 红色内圈
                )
            else:  # 普通亮度激光
                img.draw_circle(
                    center_x, center_y, self.laser_circle_radius,
                    color=image.COLOR_RED, thickness=2     # 红色圆圈
                )

            # 绘制激光点中心的小十字（更精确的中心标记）
            cross_size = 4
            img.draw_line(
                center_x - cross_size, center_y,
                center_x + cross_size, center_y,
                color=image.COLOR_WHITE, thickness=2  # 白色十字更醒目
            )
            img.draw_line(
                center_x, center_y - cross_size,
                center_x, center_y + cross_size,
                color=image.COLOR_WHITE, thickness=2
            )

            # 简化标签显示
            if not self.simple_display:
                label = f"R{i+1}"  # R表示Red激光
                img.draw_string(
                    center_x - 8, center_y - 25, label,
                    color=image.COLOR_RED, scale=0.7
                )

                # 显示亮度等级
                brightness = "★★★" if density > 0.9 else "★★" if density > 0.8 else "★"
                img.draw_string(
                    center_x - 10, center_y + 18, brightness,
                    color=image.COLOR_RED, scale=0.6
                )

    def draw_info_with_laser(self, img, rect_count, laser_count):
        """绘制信息（包含激光点信息）"""
        # 主要信息 - FPS
        fps_text = f"FPS: {self.current_fps:.1f}"
        img.draw_string(10, 10, fps_text, color=image.COLOR_GREEN, scale=1.0)

        if self.simple_display:
            # 简化模式 - 显示核心信息
            if rect_count > 0:
                count_text = f"Frames: {rect_count}"
                img.draw_string(10, 30, count_text, color=image.COLOR_BLUE, scale=0.8)

            # 显示激光点数量
            if laser_count > 0:
                laser_text = f"Red Laser: {laser_count}"
                img.draw_string(10, 50, laser_text, color=image.COLOR_RED, scale=0.8)

            # 模式提示
            if self.bright_laser_mode:
                mode_text = "Bright Laser Mode"
                img.draw_string(10, 70, mode_text, color=image.COLOR_PURPLE, scale=0.7)
            else:
                mode_text = "Laser Mode"
                img.draw_string(10, 70, mode_text, color=image.COLOR_PURPLE, scale=0.7)
        else:
            # 完整模式
            count_text = f"Frames: {rect_count}"
            img.draw_string(10, 30, count_text, color=image.COLOR_BLUE, scale=0.9)

            laser_text = f"Red Laser: {laser_count}"
            img.draw_string(10, 50, laser_text, color=image.COLOR_RED, scale=0.9)

            res_text = f"{img.width()}x{img.height()}"
            img.draw_string(10, 70, res_text, color=image.COLOR_YELLOW, scale=0.7)

            if rect_count == 0 and laser_count == 0:
                tip_text = "No detection"
                img.draw_string(10, 90, tip_text, color=image.COLOR_YELLOW, scale=0.7)
            else:
                tip_text = "Red/Yellow = Laser"
                img.draw_string(10, 90, tip_text, color=image.COLOR_RED, scale=0.6)

    def run(self):
        """主运行循环"""
        print("开始红色激光检测（高帧率版本）...")
        print("检测目标: 白色A4纸 + 黑色胶带边框 + 红色激光点")
        print("激光优化: 过亮激光星状效果处理，中心集中度检测")
        print("显示效果: 蓝色框=胶带，红色/黄色圆圈=激光点，白色十字=中心")
        print("亮度显示: ★=普通 ★★=较亮 ★★★=很亮")
        print("性能优化: 高帧率算法，激光点每帧检测")
        print("目标帧率: 15-25 FPS")
        print("分辨率: 320x240")
        print("按设备功能键退出程序")
        
        while not app.need_exit():
            try:
                # 更新帧率
                self.update_fps()
                
                # 读取图像
                img = self.cam.read()
                
                # 性能优化：不是每帧都检测
                self.frame_count += 1
                if self.frame_count % self.process_every_n_frames == 0:
                    # 同时查找白色A4纸和黑色胶带边框
                    self.last_rectangles = self.find_all_rectangles(img)

                # 每帧都检测激光点（激光点移动快，需要高频检测）
                if self.enable_laser_detection:
                    self.last_laser_points = self.find_red_laser_points(img)
                
                # 绘制结果
                self.draw_rectangles(img, self.last_rectangles)

                # 绘制激光点
                if self.enable_laser_detection and self.last_laser_points:
                    self.draw_laser_points(img, self.last_laser_points)

                # 绘制信息（包含激光点数量）
                total_detections = len(self.last_rectangles)
                laser_count = len(self.last_laser_points) if self.enable_laser_detection else 0
                self.draw_info_with_laser(img, total_detections, laser_count)
                
                # 显示图像
                self.disp.show(img)
                
                # 减少日志输出（高帧率优化）
                if self.frame_count % 90 == 0:  # 更少的日志输出
                    frame_count = len(self.last_rectangles)
                    laser_count = len(self.last_laser_points) if self.enable_laser_detection else 0
                    if frame_count > 0 or laser_count > 0:
                        print(f"FPS: {self.current_fps:.1f}, 边框: {frame_count}, 激光: {laser_count}")
                
            except Exception as e:
                print(f"运行时错误: {e}")
                time.sleep_ms(100)

def main():
    """主函数"""
    try:
        detector = NativeRectangleDetector()
        detector.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        print("程序结束")

if __name__ == "__main__":
    main()
