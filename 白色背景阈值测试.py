from maix import image, camera, display, app, time

# 摄像头和显示器初始化
cam = camera.Camera(160, 120)
disp = display.Display()

# 预设的白色阈值配置
WHITE_PRESETS = {
    1: {"name": "标准白色", "threshold": [[60, 100, -15, 15, -15, 15]]},
    2: {"name": "亮白色", "threshold": [[70, 100, -10, 10, -10, 10]]},
    3: {"name": "暗白色", "threshold": [[50, 95, -20, 20, -20, 20]]},
    4: {"name": "宽松检测", "threshold": [[45, 100, -25, 25, -25, 25]]},
    5: {"name": "严格检测", "threshold": [[75, 100, -8, 8, -8, 8]]},
    6: {"name": "偏暖白色", "threshold": [[60, 100, -15, 15, 5, 25]]},
    7: {"name": "偏冷白色", "threshold": [[60, 100, -15, 15, -25, -5]]},
    8: {"name": "灰白色", "threshold": [[40, 90, -20, 20, -20, 20]]}
}

# 当前使用的预设
current_preset = 1
white_threshold = WHITE_PRESETS[current_preset]["threshold"]

# 尺寸范围测试
SIZE_RANGES = {
    1: {"name": "近距离", "min_w": 30, "max_w": 150, "min_h": 20, "max_h": 110},
    2: {"name": "中距离", "min_w": 15, "max_w": 120, "min_h": 10, "max_h": 90},
    3: {"name": "远距离", "min_w": 8, "max_w": 80, "min_h": 6, "max_h": 60},
    4: {"name": "全范围", "min_w": 5, "max_w": 150, "min_h": 4, "max_h": 110}
}

current_size_range = 4
size_config = SIZE_RANGES[current_size_range]

def detect_white_areas():
    """检测并显示白色区域"""
    global white_threshold, size_config
    
    # 检测白色区域
    white_blobs = img.find_blobs(white_threshold,
                                pixels_threshold=30,
                                area_threshold=50,
                                merge=True)
    
    blob_count = 0
    valid_count = 0
    
    if white_blobs:
        for i, blob in enumerate(white_blobs):
            blob_count += 1
            
            # 检查尺寸是否在当前范围内
            w, h = blob[2], blob[3]
            is_valid = (w >= size_config["min_w"] and w <= size_config["max_w"] and
                       h >= size_config["min_h"] and h <= size_config["max_h"])
            
            if is_valid:
                valid_count += 1
                # 绘制有效的白色区域边框（绿色）
                img.draw_rect(blob[0], blob[1], blob[2], blob[3], 
                             color=image.COLOR_GREEN, thickness=2)
                
                # 显示区域编号
                img.draw_string(blob[0] + 2, blob[1] + 2, f"V{valid_count}", 
                               color=image.COLOR_RED, scale=1.0)
            else:
                # 绘制无效的白色区域边框（黄色）
                img.draw_rect(blob[0], blob[1], blob[2], blob[3], 
                             color=image.COLOR_YELLOW, thickness=1)
                
                # 显示区域编号
                img.draw_string(blob[0] + 2, blob[1] + 2, f"X{i+1}", 
                               color=image.COLOR_WHITE, scale=0.8)
            
            # 绘制中心点
            center_x = blob[0] + blob[2] // 2
            center_y = blob[1] + blob[3] // 2
            img.draw_cross(center_x, center_y, color=image.COLOR_RED, size=2)
            
            # 显示尺寸信息（小字体）
            size_text = f"{w}x{h}"
            img.draw_string(blob[0], blob[1] + blob[3] - 8, size_text, 
                           color=image.COLOR_WHITE, scale=0.6)
    
    return blob_count, valid_count

def draw_info():
    """绘制信息显示"""
    global current_preset, current_size_range
    
    # 检测白色区域
    total_count, valid_count = detect_white_areas()
    
    # 显示当前预设信息
    preset_info = WHITE_PRESETS[current_preset]
    preset_text = f"#{current_preset}: {preset_info['name']}"
    img.draw_string(5, 5, preset_text, color=image.COLOR_BLUE, scale=1.0)
    
    # 显示LAB阈值
    threshold = preset_info['threshold'][0]
    lab_text = f"L:{threshold[0]}-{threshold[1]} A:{threshold[2]}-{threshold[3]} B:{threshold[4]}-{threshold[5]}"
    img.draw_string(5, 18, lab_text, color=image.COLOR_WHITE, scale=0.7)
    
    # 显示尺寸范围
    size_info = SIZE_RANGES[current_size_range]
    size_text = f"Size: {size_info['name']}"
    img.draw_string(5, 30, size_text, color=image.COLOR_GREEN, scale=0.8)
    
    range_text = f"W:{size_info['min_w']}-{size_info['max_w']} H:{size_info['min_h']}-{size_info['max_h']}"
    img.draw_string(5, 42, range_text, color=image.COLOR_WHITE, scale=0.6)
    
    # 显示检测结果
    count_text = f"Total:{total_count} Valid:{valid_count}"
    img.draw_string(5, 54, count_text, color=image.COLOR_YELLOW, scale=0.8)
    
    # 显示图例
    legend = [
        "GREEN: Valid size",
        "YELLOW: Invalid size", 
        "RED: Center & Number"
    ]
    
    for i, text in enumerate(legend):
        img.draw_string(5, 70 + i*8, text, color=image.COLOR_WHITE, scale=0.5)
    
    # 显示操作提示
    help_text = "Auto switch: 6s preset, 4s size"
    img.draw_string(5, 105, help_text, color=image.COLOR_WHITE, scale=0.6)
    
    disp.show(img)

def switch_preset():
    """切换白色阈值预设"""
    global current_preset, white_threshold
    current_preset += 1
    if current_preset > len(WHITE_PRESETS):
        current_preset = 1
    
    white_threshold = WHITE_PRESETS[current_preset]["threshold"]
    print(f"切换到预设 #{current_preset}: {WHITE_PRESETS[current_preset]['name']}")

def switch_size_range():
    """切换尺寸范围"""
    global current_size_range, size_config
    current_size_range += 1
    if current_size_range > len(SIZE_RANGES):
        current_size_range = 1
    
    size_config = SIZE_RANGES[current_size_range]
    print(f"切换到尺寸范围: {size_config['name']}")

# 主程序
print("白色背景阈值测试工具")
print("=" * 50)
print("测试不同白色阈值和尺寸范围的检测效果")
print("=" * 50)
print("白色阈值预设:")
for key, preset in WHITE_PRESETS.items():
    print(f"  {key}. {preset['name']}: {preset['threshold'][0]}")
print("=" * 50)
print("尺寸范围预设:")
for key, size_range in SIZE_RANGES.items():
    print(f"  {key}. {size_range['name']}: W{size_range['min_w']}-{size_range['max_w']} H{size_range['min_h']}-{size_range['max_h']}")
print("=" * 50)
print("颜色标识:")
print("  绿色框: 尺寸有效的白色区域")
print("  黄色框: 尺寸无效的白色区域")
print("  红色十字: 区域中心点")
print("  V数字: 有效区域编号")
print("  X数字: 无效区域编号")
print("=" * 50)
print("程序将自动切换预设和尺寸范围")
print("观察不同配置下的检测效果")
print("按Ctrl+C退出程序")

# 初始化计时器
preset_switch_time = time.ticks_ms()
size_switch_time = time.ticks_ms()
PRESET_SWITCH_INTERVAL = 6000  # 6秒切换预设
SIZE_SWITCH_INTERVAL = 4000    # 4秒切换尺寸范围

print(f"当前预设: #{current_preset} - {WHITE_PRESETS[current_preset]['name']}")
print(f"当前尺寸范围: {SIZE_RANGES[current_size_range]['name']}")

while not app.need_exit():
    try:
        img = cam.read()
        
        current_time = time.ticks_ms()
        
        # 自动切换预设
        if current_time - preset_switch_time > PRESET_SWITCH_INTERVAL:
            switch_preset()
            preset_switch_time = current_time
        
        # 自动切换尺寸范围
        if current_time - size_switch_time > SIZE_SWITCH_INTERVAL:
            switch_size_range()
            size_switch_time = current_time
        
        draw_info()
        
    except KeyboardInterrupt:
        print("程序被用户中断")
        break
    except Exception as e:
        print(f"程序运行错误: {e}")
        continue

print("测试结束")
print(f"最后使用的预设: #{current_preset} - {WHITE_PRESETS[current_preset]['name']}")
print(f"最后使用的尺寸范围: {SIZE_RANGES[current_size_range]['name']}")
print("请将最佳配置应用到主程序中")
