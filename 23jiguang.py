from maix import image, camera, display, app, time,uart, pinmap  # 导入必要的模块
import math  # 导入数学模块用于数学运算
 
# pinmap.set_pin_function("A18", "UART1_RX")
# pinmap.set_pin_function("A19", "UART1_TX")
 
# device = "/dev/ttyS1"

device = "/dev/ttyS0"

serial = uart.UART(device, 115200)

cam = camera.Camera(160, 120)   
disp = display.Display()
x_red=0
y_red=0
flag=0

# cam.luma(40) #光亮
# cam.constrast(75) #对比度
# cam.saturation(75) #饱和度
black_threshold = (0, 14, -10, 10, -11, 9)
# red_jiguang_white_thresholds = [25,100,127,19,-128,111]    
# red_jiguang_black_thresholds = [0,100,8,127,-128,127]    
# thresholds = [red_jiguang_white_thresholds, red_jiguang_black_thresholds]
# green_jiguang_white_thresholds = [37,100,127,-24,-35,127]      # red
# green_jiguang_black_thresholds = [0,100,-128,127,50,127]      # red
jiguang_black_thresholds = [[44, 64, 11, 31, -11, 9], [0, 19, 19, 39, -4, 16]]
# thresholds = [[0, 80, -120, -10, 0, 30]]    # green
# thresholds = [[0, 80, 30, 100, -120, -60]]  # blue
# thresholds = [green_jiguang_white_thresholds, green_jiguang_black_thresholds]

# def red_jiguang(roi1):   #红色激光的阈值
#     blobs = img.find_blobs(thresholds,roi=roi1,merge=True)
#     for blob in blobs:
#         # img.draw_rect(blob[0], blob[1], blob[2], blob[3], image.COLOR_GREEN)
#         img.draw_cross(blob[5],blob[6],image.COLOR_RED)
#         print("激光的中心坐标是：",blob[5],blob[6])
        

def red_jiguang(roi1):
    global x_red,y_red,flag,jiguang_black_thresholds
    blobs = img.find_blobs(jiguang_black_thresholds, area_threshold=1, pixels_threshold=1, merge=True)
    for blob in blobs:
        # img.draw_rect(blob[0], blob[1], blob[2], blob[3], image.COLOR_GREEN)
        x_red=blob[5]
        y_red=blob[6]
        img.draw_cross(blob[5],blob[6],image.COLOR_GREEN,size=3)
        # print("激光的中心坐标是：",x_red,y_red)



def juxingkuang():
    global x_red,y_red,flag,jiguang_black_thresholds
    rect_corners=None
    black_blobs = img.find_blobs([black_threshold], pixels_threshold=200, area_threshold=200, merge=False)
    if black_blobs:
        for blob in black_blobs:
            x, y, w, h = blob[0], blob[1], blob[2], blob[3]
            # roi1 = img.crop(x, y, w, h)  # 提取当前斑点的ROI    
            roi1=[blob[0]-8, blob[1]-8, blob[2]+16, blob[3]+16]
            # red_jiguang(roi1) #放识别激光函数的地方
            red_jiguang(roi1)
            if blob[2]<= 155 and blob[3]<=115:                                         
                rects = img.find_rects(roi=roi1,threshold=11000)#threshol参数很重要
                for rect in rects:#找出矩形
                    x3, y3, w3, h3 = rect[0],rect[1],rect[2],rect[3],
                    # img.draw_rect(x3, y3, w3, h3, color=image.COLOR_RED, thickness=1)
                    img.draw_cross(x3 + w3 // 2, y3 + h3 // 2, color=image.COLOR_RED)
                    center_x = x3+w3//2
                    center_y = y3+h3//2
                    # print("矩形的中心坐标为：",x3 + w3 // 2, y3 + h3 // 2)            
                    rect_corners = rect.corners()
                    for corner in rect_corners:  #画出矩形的角点
                        img.draw_circle(corner[0], corner[1], 3, color=image.COLOR_WHITE, thickness=1)

                    if rect_corners is not None:
                        rect_corners_sorted = sorted(rect_corners, key=lambda corner: math.atan2(corner[1] - y3, corner[0] - x3))               
                        corner1_str = f"corner1 = ({rect_corners[0][0]},{rect_corners[0][1]})" # 
                        corner2_str = f"corner2 = ({rect_corners[1][0]},{rect_corners[1][1]})" #
                        corner3_str = f"corner3 = ({rect_corners[2][0]},{rect_corners[2][1]})" #
                        corner4_str = f"corner4 = ({rect_corners[3][0]},{rect_corners[3][1]})" #
                        #将上面得到的坐标corner1_str至corner_str按照顺时针排序,其中左上角的X轴坐标为newcorner1[0],左上角的Y坐标为newcorner1[1].
                        # 初始化新的角点坐标存储变量
                        top_left = [0, 0]
                        top_right = [0, 0]
                        bottom_right = [0, 0]
                        bottom_left = [0, 0]
                        top_left[0] = rect_corners[3][0]   #左上角坐标
                        top_left[1] = rect_corners[3][1]
                        top_right[0] = rect_corners[2][0]   #右上角坐标
                        top_right[1] = rect_corners[2][1]
                        bottom_right[0] = rect_corners[1][0]   #右下角坐标
                        bottom_right[1] = rect_corners[1][1]
                        bottom_left[0] = rect_corners[0][0]   #左下角坐标
                        bottom_left[1] = rect_corners[0][1]
            
                        # print("左上角的坐标是：",top_left[0],top_left[1])
                        # print("右上角的坐标是：",top_right[0],top_right[1])
                        # print("右下角的坐标是：",bottom_right[0],bottom_right[1])
                        # print("左下角的坐标是：",bottom_left[0],bottom_left[1])
                        # print(corner1_str + "\n" + corner2_str + "\n" + corner3_str + "\n" + corner4_str)

                        # data = [0x13, 0x23, flag, 0x33]
                        # serial.write(bytes(data))
                        #
                        if(flag == 0):
                            print("这是激光坐标是:",(int(x_red), int(y_red)))
                            print("我将发送中心坐标：",center_x, center_y)
                            data = [0x13, 0x23,int(x_red), int(y_red),center_x, center_y, 0x00]
                            serial.write(bytes(data))

                        if(abs(int(x_red)-center_x)<5 and abs(int(y_red)-center_y)<4):
                            flag =1
                            data = [0x13, 0x23,int(x_red), int(y_red),center_x, center_y, 0x01]

                        if(flag==1):
                            print("这是中心坐标是:",(int(x_red), int(y_red)))
                            print("我将发送左上角的坐标：",top_left)
                            data = [0x13, 0x23,int(x_red), int(y_red),top_left[0], top_left[1], 0x01]
                            serial.write(bytes(data))


                        if(abs(int(x_red)-top_left[0])<5 and abs(int(y_red)-top_left[1])<4):
                            flag =2
                            data = [0x13, 0x23,int(x_red), int(y_red),top_left[0], top_left[1], 0x02]

                        if(flag==2):
                            print("这是中心坐标是:",(int(x_red), int(y_red)))
                            print("我将发送右上角的坐标：",top_right[0],top_right[1])
                            data = [0x13, 0x23,int(x_red), int(y_red),top_right[0],top_right[1], 0x02]
                            serial.write(bytes(data))

                        
                        if(abs(int(x_red)-top_right[0])<5 and abs(int(y_red)-top_right[1])<4):
                            flag =3
                            data = [0x13, 0x23,int(x_red), int(y_red),top_right[0],top_right[1], 0x03]

                        if(flag==3):
                            print("这是中心坐标是:",(int(x_red), int(y_red)))
                            print("我将发送右下角的坐标：",bottom_right[0],bottom_right[1])
                            data = [0x13, 0x23,int(x_red), int(y_red),bottom_right[0],bottom_right[1],0x03]
                            serial.write(bytes(data))

                        
                        if(abs(int(x_red)-bottom_right[0])<5 and abs(int(y_red)-bottom_right[1])<4):
                            flag =4
                            data = [0x13, 0x23,int(x_red), int(y_red),bottom_right[0],bottom_right[1], 0x04]

                        if(flag==4):
                            print("这是中心坐标是:",(int(x_red), int(y_red)))
                            print("我将发送左下角的坐标：",bottom_left[0],bottom_left[1])
                            data = [0x13, 0x23,int(x_red), int(y_red),bottom_left[0],bottom_left[1],0x04]
                            serial.write(bytes(data))


                        if(abs(int(x_red)-bottom_left[0])<5 and abs(int(y_red)-bottom_left[1])<4):
                            flag =5
                            data = [0x13, 0x23,int(x_red), int(y_red),bottom_left[0],bottom_left[1], 0x05]

                        if(flag==5):
                            print("这是中心坐标是:",(int(x_red), int(y_red)))
                            print("我将发送左上角的坐标：",top_left[0],top_left[1])
                            data = [0x13, 0x23,int(x_red), int(y_red),top_left[0],top_left[1],0x05]
                            serial.write(bytes(data)) 
                    else:
                        print("没有找到坐标")
    disp.show(img)
while True:
    t = time.ticks_ms()  # 记录当前时间（毫秒）
    img = cam.read()  # 从摄像头获取一帧图像
    # img = cam.read().lens_corr(strength=1.8, zoom=1.0, x_corr=0.0, y_corr=0.0)
    juxingkuang()                                
    # disp.show(img)

