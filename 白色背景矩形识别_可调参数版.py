from maix import image, camera, display, app, time, uart

# ==================== 可调参数区域 ====================
# 连续识别参数（用户可根据需要调整）
REQUIRED_SUCCESS = 2       # 需要连续成功的次数 (1-5, 推荐2)
COORDINATE_TOLERANCE = 5   # 坐标变化容忍度 (1-10像素, 推荐5)

# 检测范围参数
MIN_WIDTH = 8              # 最小宽度 (支持远距离)
MAX_WIDTH = 150            # 最大宽度 (支持近距离)
MIN_HEIGHT = 6             # 最小高度
MAX_HEIGHT = 110           # 最大高度

# 白色背景阈值 (LAB色彩空间)
WHITE_L_MIN = 60           # L通道最小值 (亮度)
WHITE_L_MAX = 100          # L通道最大值
WHITE_A_MIN = -15          # A通道最小值 (绿-红)
WHITE_A_MAX = 15           # A通道最大值
WHITE_B_MIN = -15          # B通道最小值 (蓝-黄)
WHITE_B_MAX = 15           # B通道最大值

# 检测阈值参数
RECT_THRESHOLD = 8000      # 矩形检测阈值 (越小越敏感)
MIN_PIXELS = 40            # 最小像素数
MIN_AREA = 50              # 最小面积
MAX_AREA = 8000            # 最大面积
# ==================== 参数区域结束 ====================

# 串口配置
device = "/dev/ttyS0"
serial = uart.UART(device, 115200)

# 摄像头和显示器初始化
cam = camera.Camera(160, 120)
disp = display.Display()

# 根据用户参数构建阈值
white_threshold = [[WHITE_L_MIN, WHITE_L_MAX, WHITE_A_MIN, WHITE_A_MAX, WHITE_B_MIN, WHITE_B_MAX]]
black_threshold = [[0, 20, -15, 15, -15, 15]]

# 连续识别状态变量
success_count = 0
last_detection = None

def is_detection_stable(current_detection):
    """检查检测结果稳定性"""
    global last_detection
    
    if last_detection is None:
        return False
    
    # 比较坐标和尺寸变化
    center_diff_x = abs(current_detection['center_x'] - last_detection['center_x'])
    center_diff_y = abs(current_detection['center_y'] - last_detection['center_y'])
    size_diff_w = abs(current_detection['width'] - last_detection['width'])
    size_diff_h = abs(current_detection['height'] - last_detection['height'])
    
    return (center_diff_x <= COORDINATE_TOLERANCE and 
            center_diff_y <= COORDINATE_TOLERANCE and
            size_diff_w <= COORDINATE_TOLERANCE and 
            size_diff_h <= COORDINATE_TOLERANCE)

def detect_rectangle():
    """主检测函数"""
    global success_count, last_detection
    
    detection_found = False
    
    # 检测白色背景区域
    white_blobs = img.find_blobs(white_threshold, 
                                pixels_threshold=MIN_PIXELS,
                                area_threshold=MIN_AREA,
                                merge=True)
    
    if white_blobs:
        for white_blob in white_blobs:
            blob_w, blob_h = white_blob[2], white_blob[3]
            blob_area = blob_w * blob_h
            
            # 尺寸检查
            if (blob_w < MIN_WIDTH or blob_w > MAX_WIDTH or
                blob_h < MIN_HEIGHT or blob_h > MAX_HEIGHT or
                blob_area < MIN_AREA or blob_area > MAX_AREA):
                continue
            
            # 设置ROI
            margin = max(5, min(blob_w // 10, blob_h // 10))
            roi = [max(0, white_blob[0] - margin), 
                  max(0, white_blob[1] - margin),
                  min(160, white_blob[2] + 2*margin), 
                  min(120, white_blob[3] + 2*margin)]
            
            # 尝试矩形检测
            try:
                rects = img.find_rects(roi=roi, threshold=RECT_THRESHOLD)
                if rects:
                    for rect in rects:
                        x3, y3, w3, h3 = rect[0], rect[1], rect[2], rect[3]
                        
                        # 验证矩形尺寸
                        if (w3 >= MIN_WIDTH and w3 <= MAX_WIDTH and
                            h3 >= MIN_HEIGHT and h3 <= MAX_HEIGHT):
                            
                            # 计算中心点和角点
                            center_x = x3 + w3 // 2
                            center_y = y3 + h3 // 2
                            
                            # 绘制检测结果
                            img.draw_cross(center_x, center_y, color=image.COLOR_RED, size=5)
                            
                            rect_corners = rect.corners()
                            point_size = max(2, min(6, w3 // 20))
                            
                            for corner in rect_corners:
                                img.draw_circle(corner[0], corner[1], point_size, 
                                              color=image.COLOR_WHITE, thickness=2)
                            
                            # 提取角点坐标
                            top_left = [rect_corners[3][0], rect_corners[3][1]]
                            top_right = [rect_corners[2][0], rect_corners[2][1]]
                            bottom_right = [rect_corners[1][0], rect_corners[1][1]]
                            bottom_left = [rect_corners[0][0], rect_corners[0][1]]
                            
                            # 创建检测结果
                            current_detection = {
                                'center_x': center_x,
                                'center_y': center_y,
                                'width': w3,
                                'height': h3,
                                'top_left': top_left,
                                'top_right': top_right,
                                'bottom_right': bottom_right,
                                'bottom_left': bottom_left
                            }
                            
                            # 稳定性检查和计数
                            if is_detection_stable(current_detection):
                                success_count += 1
                                print(f"稳定检测 {success_count}/{REQUIRED_SUCCESS} - 中心({center_x},{center_y}) 尺寸({w3}x{h3})")
                            else:
                                success_count = 1
                                print(f"新检测 {success_count}/{REQUIRED_SUCCESS} - 中心({center_x},{center_y}) 尺寸({w3}x{h3})")
                            
                            # 更新上次检测结果
                            last_detection = current_detection
                            detection_found = True
                            
                            # 检查是否达到发送条件
                            if success_count >= REQUIRED_SUCCESS:
                                # 发送坐标数据
                                data = [0x13, 0x23, center_x, center_y,
                                       top_left[0], top_left[1],
                                       top_right[0], top_right[1],
                                       bottom_right[0], bottom_right[1],
                                       bottom_left[0], bottom_left[1]]
                                
                                try:
                                    serial.write(bytes(data))
                                    print(f"✓ 坐标已发送: 中心({center_x},{center_y})")
                                except Exception as e:
                                    print(f"✗ 发送失败: {e}")
                            
                            break
            except Exception as e:
                print(f"检测错误: {e}")
    
    # 如果没有检测到矩形，重置状态
    if not detection_found:
        if last_detection is not None:
            success_count = 0
            last_detection = None
            print("检测丢失，重置计数器")

# 主程序
print("白色背景矩形识别程序 - 可调参数版")
print("=" * 60)
print("当前参数配置:")
print(f"  连续成功要求: {REQUIRED_SUCCESS} 次")
print(f"  坐标容忍度: ±{COORDINATE_TOLERANCE} 像素")
print(f"  尺寸范围: {MIN_WIDTH}x{MIN_HEIGHT} 到 {MAX_WIDTH}x{MAX_HEIGHT}")
print(f"  白色阈值: L({WHITE_L_MIN}-{WHITE_L_MAX}) A({WHITE_A_MIN}-{WHITE_A_MAX}) B({WHITE_B_MIN}-{WHITE_B_MAX})")
print(f"  检测阈值: {RECT_THRESHOLD}")
print("=" * 60)
print("如需调整参数，请修改文件顶部的参数区域")
print("按Ctrl+C退出程序")
print("=" * 60)

while not app.need_exit():
    try:
        img = cam.read()
        detect_rectangle()
        disp.show(img)
        
    except KeyboardInterrupt:
        print("程序被用户中断")
        break
    except Exception as e:
        print(f"程序运行错误: {e}")
        success_count = 0
        last_detection = None
        continue

print("程序结束")
