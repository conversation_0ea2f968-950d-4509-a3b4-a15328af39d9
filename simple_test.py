#!/usr/bin/env python3
"""
简单测试脚本 - 检查代码语法和基本功能
"""

import math

def test_math_functions():
    """测试math模块功能"""
    print("测试math模块功能...")
    
    # 测试欧几里得距离计算
    def calculate_distance(x1, y1, x2, y2):
        dx = x2 - x1
        dy = y2 - y1
        return math.sqrt(dx * dx + dy * dy)
    
    distance = calculate_distance(0, 0, 3, 4)
    print(f"✓ 距离计算: (0,0) 到 (3,4) = {distance}")
    
    # 测试角度计算
    angle = math.acos(0.5)  # 60度
    angle_degrees = math.degrees(angle)
    print(f"✓ 角度计算: acos(0.5) = {angle_degrees}度")
    
    # 测试圆度计算
    area = 100
    perimeter = 35.45
    circularity = 4 * math.pi * area / (perimeter * perimeter)
    print(f"✓ 圆度计算: {circularity:.3f}")
    
    print("math模块测试完成！\n")

def test_coordinate_similarity():
    """测试坐标相似性判断"""
    print("测试坐标相似性判断...")
    
    def coordinates_similar(coord1, coord2, tolerance=10):
        """使用欧几里得距离检查坐标相似性"""
        if 'center' in coord1 and 'center' in coord2:
            dx = coord1['center'][0] - coord2['center'][0]
            dy = coord1['center'][1] - coord2['center'][1]
            distance = math.sqrt(dx * dx + dy * dy)
            return distance <= tolerance
        return False
    
    # 测试用例
    coord1 = {'center': (100, 100)}
    coord2 = {'center': (105, 103)}
    coord3 = {'center': (120, 130)}
    
    result1 = coordinates_similar(coord1, coord2)
    result2 = coordinates_similar(coord1, coord3)
    
    print(f"✓ 坐标(100,100)和(105,103)相似: {result1}")
    print(f"✓ 坐标(100,100)和(120,130)相似: {result2}")
    print("坐标相似性测试完成！\n")

def test_rectangle_quality():
    """测试矩形质量评估"""
    print("测试矩形质量评估...")
    
    def calculate_rectangle_quality(corners):
        """计算矩形质量分数"""
        if not corners or len(corners) != 4:
            return 0
        
        # 计算四条边的长度
        sides = []
        for i in range(4):
            p1 = corners[i]
            p2 = corners[(i + 1) % 4]
            side_length = math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
            sides.append(side_length)
        
        # 计算对边长度比
        opposite_ratio1 = min(sides[0], sides[2]) / max(sides[0], sides[2])
        opposite_ratio2 = min(sides[1], sides[3]) / max(sides[1], sides[3])
        
        # 计算对角线长度
        diag1 = math.sqrt((corners[2][0] - corners[0][0])**2 + (corners[2][1] - corners[0][1])**2)
        diag2 = math.sqrt((corners[3][0] - corners[1][0])**2 + (corners[3][1] - corners[1][1])**2)
        
        # 对角线长度比
        diagonal_ratio = min(diag1, diag2) / max(diag1, diag2)
        
        # 简化的质量分数
        quality = (opposite_ratio1 * 0.4 + opposite_ratio2 * 0.4 + diagonal_ratio * 0.2) * 100
        return max(0, min(100, quality))
    
    # 测试完美矩形
    perfect_rect = [[0, 0], [100, 0], [100, 50], [0, 50]]
    quality1 = calculate_rectangle_quality(perfect_rect)
    
    # 测试不规则四边形
    irregular_rect = [[0, 0], [100, 10], [90, 60], [10, 45]]
    quality2 = calculate_rectangle_quality(irregular_rect)
    
    print(f"✓ 完美矩形质量: {quality1:.1f}")
    print(f"✓ 不规则四边形质量: {quality2:.1f}")
    print("矩形质量评估测试完成！\n")

def test_laser_quality():
    """测试激光点质量评估"""
    print("测试激光点质量评估...")
    
    def calculate_laser_quality(area, perimeter):
        """计算激光点质量分数"""
        # 基于面积的质量评估
        ideal_area = 50  # 假设理想面积
        area_score = 1.0 - abs(area - ideal_area) / ideal_area
        
        # 基于圆度的质量评估
        if perimeter > 0:
            circularity = 4 * math.pi * area / (perimeter * perimeter)
            circularity_score = min(1.0, circularity)
        else:
            circularity_score = 0
        
        # 综合质量分数
        quality = (area_score * 0.5 + circularity_score * 0.5) * 100
        return max(0, min(100, quality))
    
    # 测试理想圆形激光点
    ideal_area = 50
    ideal_perimeter = 2 * math.pi * math.sqrt(ideal_area / math.pi)
    quality1 = calculate_laser_quality(ideal_area, ideal_perimeter)
    
    # 测试不规则激光点
    irregular_area = 45
    irregular_perimeter = 30
    quality2 = calculate_laser_quality(irregular_area, irregular_perimeter)
    
    print(f"✓ 理想圆形激光点质量: {quality1:.1f}")
    print(f"✓ 不规则激光点质量: {quality2:.1f}")
    print("激光点质量评估测试完成！\n")

def main():
    """主函数"""
    print("=== Math模块优化功能测试 ===\n")
    
    try:
        test_math_functions()
        test_coordinate_similarity()
        test_rectangle_quality()
        test_laser_quality()
        
        print("🎉 所有测试通过！Math模块优化功能正常工作。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
