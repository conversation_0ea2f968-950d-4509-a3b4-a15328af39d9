from maix import image, camera, display, app, time

# 摄像头和显示器初始化
cam = camera.Camera(160, 120)
disp = display.Display()

# 检测参数
green_threshold = [[30, 80, -40, -10, -20, 20]]
black_threshold = [[0, 14, -10, 10, -11, 9]]

# 检测模式
DETECTION_MODES = {
    1: {"name": "Standard Rect", "color": image.COLOR_RED},
    2: {"name": "Line Segments", "color": image.COLOR_BLUE},
    3: {"name": "Black Blobs", "color": image.COLOR_YELLOW},
    4: {"name": "Combined", "color": image.COLOR_WHITE}
}

current_mode = 1
fps_counter = 0
fps_start_time = 0
current_fps = 0

def detect_standard_rectangles(roi):
    """标准矩形检测"""
    rectangles = []
    try:
        rects = img.find_rects(roi=roi, threshold=12000)
        for rect in rects:
            if rect[2] >= 20 and rect[2] <= 200:
                # 绘制标准矩形
                img.draw_rect(rect[0], rect[1], rect[2], rect[3], 
                             color=image.COLOR_RED, thickness=2)
                rectangles.append(f"Std: {rect[2]}x{rect[3]}")
    except Exception as e:
        print(f"标准检测错误: {e}")
    return rectangles

def detect_line_segments(roi):
    """线段检测"""
    rectangles = []
    try:
        lines = img.find_line_segments(roi=roi, merge_distance=5, max_theta_difference=15)
        
        if len(lines) >= 3:
            # 绘制检测到的线段
            for line in lines[:6]:  # 只显示前6条线段
                img.draw_line(line.x1(), line.y1(), line.x2(), line.y2(), 
                             color=image.COLOR_BLUE, thickness=1)
            
            # 计算边界框
            if lines:
                lines.sort(key=lambda line: line.length(), reverse=True)
                main_lines = lines[:4]
                
                min_x = min([min(line.x1(), line.x2()) for line in main_lines])
                max_x = max([max(line.x1(), line.x2()) for line in main_lines])
                min_y = min([min(line.y1(), line.y2()) for line in main_lines])
                max_y = max([max(line.y1(), line.y2()) for line in main_lines])
                
                rect_w = max_x - min_x
                rect_h = max_y - min_y
                
                if rect_w >= 20 and rect_w <= 200 and rect_h > 10:
                    # 绘制边界框
                    img.draw_rect(min_x, min_y, rect_w, rect_h, 
                                 color=image.COLOR_BLUE, thickness=2)
                    rectangles.append(f"Line: {rect_w}x{rect_h}")
    except Exception as e:
        print(f"线段检测错误: {e}")
    return rectangles

def detect_black_blobs(roi):
    """黑色区域检测"""
    rectangles = []
    try:
        black_blobs = img.find_blobs(black_threshold,
                                   roi=roi,
                                   pixels_threshold=50,
                                   area_threshold=50,
                                   merge=True)
        
        if black_blobs:
            for blob in black_blobs:
                if blob[2] >= 20 and blob[2] <= 200:
                    # 绘制黑色区域
                    img.draw_rect(blob[0], blob[1], blob[2], blob[3], 
                                 color=image.COLOR_YELLOW, thickness=2)
                    rectangles.append(f"Blob: {blob[2]}x{blob[3]}")
    except Exception as e:
        print(f"黑色区域检测错误: {e}")
    return rectangles

def test_detection_methods():
    """测试不同的检测方法"""
    # 检测绿色背景
    green_blobs = img.find_blobs(green_threshold, 
                                pixels_threshold=300,
                                area_threshold=300,
                                merge=True)
    
    detection_results = []
    
    if green_blobs:
        for green_blob in green_blobs:
            if green_blob[2] < 20 or green_blob[2] > 200:
                continue
            
            # 绘制绿色区域
            img.draw_rect(green_blob[0], green_blob[1], green_blob[2], green_blob[3], 
                         color=image.COLOR_GREEN, thickness=1)
            
            # 设置ROI
            margin = 10
            roi = [max(0, green_blob[0] - margin), 
                  max(0, green_blob[1] - margin),
                  min(160, green_blob[2] + 2*margin), 
                  min(120, green_blob[3] + 2*margin)]
            
            # 根据当前模式进行检测
            if current_mode == 1:
                results = detect_standard_rectangles(roi)
                detection_results.extend(results)
            elif current_mode == 2:
                results = detect_line_segments(roi)
                detection_results.extend(results)
            elif current_mode == 3:
                results = detect_black_blobs(roi)
                detection_results.extend(results)
            elif current_mode == 4:
                # 组合检测
                std_results = detect_standard_rectangles(roi)
                line_results = detect_line_segments(roi)
                blob_results = detect_black_blobs(roi)
                detection_results.extend(std_results + line_results + blob_results)
    
    return detection_results

def draw_info():
    """绘制信息显示"""
    global fps_counter, fps_start_time, current_fps, current_mode
    
    # 计算FPS
    fps_counter += 1
    current_time = time.ticks_ms()
    
    if current_time - fps_start_time >= 1000:
        current_fps = fps_counter * 1000 / (current_time - fps_start_time)
        fps_counter = 0
        fps_start_time = current_time
    
    # 显示FPS
    fps_text = f"FPS: {current_fps:.1f}"
    img.draw_string(5, 5, fps_text, color=image.COLOR_GREEN, scale=1.2)
    
    # 显示当前检测模式
    mode_info = DETECTION_MODES[current_mode]
    mode_text = f"Mode: {mode_info['name']}"
    img.draw_string(5, 20, mode_text, color=mode_info['color'], scale=1.0)
    
    # 执行检测并显示结果
    results = test_detection_methods()
    
    # 显示检测结果数量
    result_text = f"Found: {len(results)}"
    img.draw_string(5, 35, result_text, color=image.COLOR_WHITE, scale=1.0)
    
    # 显示具体结果（最多显示3个）
    for i, result in enumerate(results[:3]):
        result_detail = result[:12]  # 限制长度
        img.draw_string(5, 50 + i*10, result_detail, color=image.COLOR_WHITE, scale=0.8)
    
    # 显示操作提示
    help_text = "Auto switch every 3s"
    img.draw_string(5, 105, help_text, color=image.COLOR_BLUE, scale=0.7)
    
    disp.show(img)

def switch_mode():
    """切换检测模式"""
    global current_mode
    
    current_mode += 1
    if current_mode > len(DETECTION_MODES):
        current_mode = 1
    
    mode_info = DETECTION_MODES[current_mode]
    print(f"切换到模式 {current_mode}: {mode_info['name']}")

# 主程序
print("边框检测策略测试工具")
print("=" * 40)
print("检测模式:")
for key, mode in DETECTION_MODES.items():
    print(f"  {key}. {mode['name']}")
print("=" * 40)
print("程序将每3秒自动切换检测模式")
print("观察不同模式对有突出边框的检测效果")
print("按Ctrl+C退出程序")
print("=" * 40)

# 初始化
fps_start_time = time.ticks_ms()
auto_switch_time = time.ticks_ms()
AUTO_SWITCH_INTERVAL = 3000  # 3秒自动切换

print(f"当前模式: {current_mode} - {DETECTION_MODES[current_mode]['name']}")

while not app.need_exit():
    try:
        img = cam.read()
        
        # 自动切换模式
        current_time = time.ticks_ms()
        if current_time - auto_switch_time > AUTO_SWITCH_INTERVAL:
            switch_mode()
            auto_switch_time = current_time
        
        draw_info()
        
    except KeyboardInterrupt:
        print("程序被用户中断")
        break
    except Exception as e:
        print(f"程序运行错误: {e}")
        continue

print("测试结束")
print(f"最后使用的模式: {current_mode} - {DETECTION_MODES[current_mode]['name']}")
print("根据测试结果选择最适合的检测策略")
