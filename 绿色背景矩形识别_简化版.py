from maix import image, camera, display, app, time, uart  # 导入MaixCam Pro必要的模块

# 串口配置
device = "/dev/ttyS0"
serial = uart.UART(device, 115200)

# 摄像头和显示器初始化
cam = camera.Camera(160, 120)
disp = display.Display()

# LAB色彩空间绿色背景阈值设置
# 绿色在LAB色彩空间中的特征：
# L: 亮度适中 (30-80)
# A: 负值，偏绿色 (-40到-10)
# B: 可能偏黄或中性 (-20到20)
green_threshold = [[30, 80, -40, -10, -20, 20]]

# 黑色边框阈值（用于检测矩形边框）
black_threshold = [[0, 14, -10, 10, -11, 9]]

# 宽度阈值设置
min_width_threshold = 20   # 最小宽度阈值
max_width_threshold = 200  # 最大宽度阈值

# FPS计算相关变量
fps_counter = 0
fps_start_time = 0
current_fps = 0

def detect_green_rectangle():
    """
    检测绿色背景上的矩形框
    先检测绿色区域，再在绿色区域内检测黑色边框矩形
    """
    rect_corners = None
    
    # 第一步：检测绿色背景区域
    green_blobs = img.find_blobs(green_threshold, 
                                pixels_threshold=300,  # 绿色区域需要足够大
                                area_threshold=300,
                                merge=True)            # 合并相邻的绿色区域
    
    if green_blobs:
        for green_blob in green_blobs:
            # 绿色区域宽度阈值检测
            if green_blob[2] < min_width_threshold or green_blob[2] > max_width_threshold:
                continue
            
            # 不显示绿色区域边框（注释掉）
            # img.draw_rect(green_blob[0], green_blob[1], green_blob[2], green_blob[3],
            #              color=image.COLOR_BLUE, thickness=2)
            
            # 设置ROI为绿色区域
            green_roi = [green_blob[0], green_blob[1], green_blob[2], green_blob[3]]
            
            # 第二步：在绿色区域内检测黑色边框
            black_blobs = img.find_blobs(black_threshold,
                                       roi=green_roi,
                                       pixels_threshold=100,
                                       area_threshold=100,
                                       merge=False)
            
            if black_blobs:
                for black_blob in black_blobs:
                    # 黑色边框宽度检测
                    if black_blob[2] < 10 or black_blob[2] > green_blob[2]:
                        continue
                    
                    # 设置矩形检测ROI
                    rect_roi = [black_blob[0]-5, black_blob[1]-5, 
                               black_blob[2]+10, black_blob[3]+10]
                    
                    # 第三步：检测矩形
                    if black_blob[2] <= 155 and black_blob[3] <= 115:
                        rects = img.find_rects(roi=rect_roi, threshold=15000)
                        
                        for rect in rects:
                            x3, y3, w3, h3 = rect[0], rect[1], rect[2], rect[3]
                            
                            # 再次检查矩形宽度
                            if w3 < min_width_threshold or w3 > max_width_threshold:
                                continue
                            
                            # 计算并绘制矩形中心点
                            center_x = x3 + w3 // 2
                            center_y = y3 + h3 // 2
                            img.draw_cross(center_x, center_y, color=image.COLOR_RED, size=5)
                            
                            print(f"绿色背景矩形中心坐标：({center_x}, {center_y})")
                            print(f"矩形尺寸：宽度={w3}, 高度={h3}")
                            
                            # 获取矩形的四个角点
                            rect_corners = rect.corners()
                            
                            # 绘制角点
                            for corner in rect_corners:
                                img.draw_circle(corner[0], corner[1], 4, 
                                              color=image.COLOR_WHITE, thickness=2)
                            
                            if rect_corners is not None:
                                # 提取四个角点坐标
                                top_left = [rect_corners[3][0], rect_corners[3][1]]
                                top_right = [rect_corners[2][0], rect_corners[2][1]]
                                bottom_right = [rect_corners[1][0], rect_corners[1][1]]
                                bottom_left = [rect_corners[0][0], rect_corners[0][1]]
                                
                                # 打印角点坐标
                                print(f"左上角：({top_left[0]}, {top_left[1]})")
                                print(f"右上角：({top_right[0]}, {top_right[1]})")
                                print(f"右下角：({bottom_right[0]}, {bottom_right[1]})")
                                print(f"左下角：({bottom_left[0]}, {bottom_left[1]})")
                                
                                # 发送矩形坐标数据
                                data = [0x13, 0x23, center_x, center_y,
                                       top_left[0], top_left[1],
                                       top_right[0], top_right[1],
                                       bottom_right[0], bottom_right[1],
                                       bottom_left[0], bottom_left[1]]
                                
                                try:
                                    serial.write(bytes(data))
                                    print("✓ 绿色背景矩形坐标数据发送成功")
                                except Exception as e:
                                    print(f"✗ 串口发送失败: {e}")
                            else:
                                print("⚠ 未找到矩形角点")

def draw_fps():
    """
    计算并在屏幕上显示FPS
    """
    global fps_counter, fps_start_time, current_fps
    
    fps_counter += 1
    current_time = time.ticks_ms()
    
    # 每秒更新一次FPS显示
    if current_time - fps_start_time >= 1000:
        current_fps = fps_counter * 1000 / (current_time - fps_start_time)
        fps_counter = 0
        fps_start_time = current_time
    
    # 在屏幕左上角显示FPS
    fps_text = f"FPS: {current_fps:.1f}"
    img.draw_string(5, 5, fps_text, color=image.COLOR_GREEN, scale=1.5)
    
    # 在屏幕右上角显示模式信息
    mode_text = "Green BG"
    img.draw_string(90, 5, mode_text, color=image.COLOR_BLUE, scale=1.2)
    
    # 显示处理后的图像
    disp.show(img)

# 主程序
print("绿色背景矩形检测程序启动...")
print("LAB绿色阈值参数：")
print("L(亮度): 30-80 (中等亮度)")
print("A(绿红): -40到-10 (偏绿色)")
print("B(蓝黄): -20到20 (中性)")
print(f"宽度过滤范围: {min_width_threshold}-{max_width_threshold}像素")
print("提示：将黑色边框的矩形放在绿色背景前")
print("按Ctrl+C退出程序")

# 初始化FPS计时器
fps_start_time = time.ticks_ms()

while not app.need_exit():
    try:
        img = cam.read()  # 从摄像头获取一帧图像
        detect_green_rectangle()  # 检测绿色背景上的矩形
        draw_fps()  # 计算并显示FPS
        
    except KeyboardInterrupt:
        print("程序被用户中断")
        break
    except Exception as e:
        print(f"程序运行错误: {e}")
        continue

print("程序结束")
