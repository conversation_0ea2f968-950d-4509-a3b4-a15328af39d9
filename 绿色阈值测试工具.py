from maix import image, camera, display, app, time

# 摄像头和显示器初始化
cam = camera.Camera(160, 120)
disp = display.Display()

# 预设的绿色阈值配置
GREEN_PRESETS = {
    1: {"name": "标准绿色", "threshold": [[30, 80, -40, -10, -20, 20]]},
    2: {"name": "鲜绿色", "threshold": [[25, 75, -45, -15, -25, 15]]},
    3: {"name": "深绿色", "threshold": [[15, 60, -50, -20, -30, 10]]},
    4: {"name": "浅绿色", "threshold": [[40, 90, -35, -5, -15, 25]]},
    5: {"name": "黄绿色", "threshold": [[35, 85, -30, -5, 0, 40]]},
    6: {"name": "蓝绿色", "threshold": [[30, 80, -45, -15, -40, 0]]},
    7: {"name": "宽松检测", "threshold": [[20, 90, -50, 0, -30, 30]]},
    8: {"name": "严格检测", "threshold": [[35, 75, -35, -15, -15, 15]]}
}

# 当前使用的预设
current_preset = 1
green_threshold = GREEN_PRESETS[current_preset]["threshold"]

# FPS计算变量
fps_counter = 0
fps_start_time = 0
current_fps = 0

def detect_green_areas():
    """检测并显示绿色区域"""
    global green_threshold
    
    # 检测绿色区域
    green_blobs = img.find_blobs(green_threshold,
                                pixels_threshold=100,
                                area_threshold=100,
                                merge=True)
    
    blob_count = len(green_blobs) if green_blobs else 0
    
    if green_blobs:
        for i, blob in enumerate(green_blobs):
            # 绘制绿色区域边框
            img.draw_rect(blob[0], blob[1], blob[2], blob[3], 
                         color=image.COLOR_YELLOW, thickness=2)
            
            # 显示区域编号
            img.draw_string(blob[0] + 2, blob[1] + 2, str(i+1), 
                           color=image.COLOR_WHITE, scale=1.2)
            
            # 绘制中心点
            center_x = blob[0] + blob[2] // 2
            center_y = blob[1] + blob[3] // 2
            img.draw_cross(center_x, center_y, color=image.COLOR_RED, size=3)
    
    return blob_count

def draw_info():
    """绘制信息显示"""
    global fps_counter, fps_start_time, current_fps, current_preset
    
    # 计算FPS
    fps_counter += 1
    current_time = time.ticks_ms()
    
    if current_time - fps_start_time >= 1000:
        current_fps = fps_counter * 1000 / (current_time - fps_start_time)
        fps_counter = 0
        fps_start_time = current_time
    
    # 显示FPS
    fps_text = f"FPS: {current_fps:.1f}"
    img.draw_string(5, 5, fps_text, color=image.COLOR_GREEN, scale=1.2)
    
    # 显示当前预设信息
    preset_info = GREEN_PRESETS[current_preset]
    preset_text = f"#{current_preset}: {preset_info['name']}"
    img.draw_string(5, 20, preset_text, color=image.COLOR_BLUE, scale=1.0)
    
    # 显示LAB阈值
    threshold = preset_info['threshold'][0]
    lab_text = f"L:{threshold[0]}-{threshold[1]} A:{threshold[2]}-{threshold[3]} B:{threshold[4]}-{threshold[5]}"
    img.draw_string(5, 35, lab_text, color=image.COLOR_WHITE, scale=0.8)
    
    # 检测绿色区域并显示数量
    blob_count = detect_green_areas()
    count_text = f"Green Areas: {blob_count}"
    img.draw_string(5, 50, count_text, color=image.COLOR_YELLOW, scale=1.0)

    # 显示操作提示
    help_text = "Press USER key to switch preset"
    img.draw_string(5, 105, help_text, color=image.COLOR_WHITE, scale=0.7)
    
    disp.show(img)

def switch_preset():
    """切换到下一个预设"""
    global current_preset, green_threshold
    
    current_preset += 1
    if current_preset > len(GREEN_PRESETS):
        current_preset = 1
    
    green_threshold = GREEN_PRESETS[current_preset]["threshold"]
    
    print(f"切换到预设 #{current_preset}: {GREEN_PRESETS[current_preset]['name']}")
    print(f"LAB阈值: {green_threshold[0]}")

# 主程序
print("绿色阈值测试工具")
print("=" * 40)
print("可用预设:")
for key, preset in GREEN_PRESETS.items():
    print(f"  {key}. {preset['name']}: {preset['threshold'][0]}")
print("=" * 40)
print("操作说明:")
print("- 按设备上的USER键切换预设")
print("- 观察屏幕上的绿色区域检测效果")
print("- 选择最适合您绿色背景的预设")
print("- 按Ctrl+C退出程序")
print("=" * 40)

# 初始化
fps_start_time = time.ticks_ms()
last_key_time = 0

# 尝试导入按键模块（如果可用）
try:
    from maix.peripheral import key
    user_key = key.Key()
    key_available = True
    print("USER键功能已启用")
except:
    key_available = False
    print("USER键功能不可用，将自动循环切换预设")

print(f"当前预设: #{current_preset} - {GREEN_PRESETS[current_preset]['name']}")

auto_switch_time = time.ticks_ms()
AUTO_SWITCH_INTERVAL = 5000  # 5秒自动切换（当按键不可用时）

while not app.need_exit():
    try:
        img = cam.read()
        
        # 检查按键切换
        if key_available:
            try:
                if user_key.is_pressed():
                    current_time = time.ticks_ms()
                    if current_time - last_key_time > 500:  # 防抖动
                        switch_preset()
                        last_key_time = current_time
            except:
                pass
        else:
            # 自动切换预设（当按键不可用时）
            current_time = time.ticks_ms()
            if current_time - auto_switch_time > AUTO_SWITCH_INTERVAL:
                switch_preset()
                auto_switch_time = current_time
        
        draw_info()
        
    except KeyboardInterrupt:
        print("程序被用户中断")
        break
    except Exception as e:
        print(f"程序运行错误: {e}")
        continue

print("测试结束")
print(f"最后使用的预设: #{current_preset} - {GREEN_PRESETS[current_preset]['name']}")
print("请将此预设应用到主程序中")
