from maix import image, camera, display, app, time, uart, pwm, pinmap  # MaixCam Pro支持直接从maix导入

# 串口配置 (解除注释)
device = "/dev/ttyS0"
serial = uart.UART(device, 115200)

# 舵机控制配置 (已注释) - 根据MaixCam Pro官方文档修正
# # 配置引脚功能为PWM (根据MaixCam Pro引脚定义)
# pinmap.set_pin_function("A19", "PWM7")  # 水平舵机 (左右转动) - PWM7通道
# pinmap.set_pin_function("A18", "PWM6")  # 垂直舵机 (上下转动) - PWM6通道

# # 初始化PWM (MG996R 270°舵机标准频率50Hz)
# # 根据官方文档，PWM构造函数参数为 (pwm_id, freq, duty, enable)
# servo_horizontal = pwm.PWM(7, freq=50, duty=7.5, enable=True)  # PWM通道7，初始中位(7.5%占空比)
# servo_vertical = pwm.PWM(6, freq=50, duty=7.5, enable=True)    # PWM通道6，初始中位(7.5%占空比)

# 舵机初始化信息 (已注释)
# print("正在初始化舵机...")
# print("A19引脚 -> PWM7 -> 水平舵机 (左右转动)")
# print("A18引脚 -> PWM6 -> 垂直舵机 (上下转动)")

# # 舵机初始化验证 - 执行复位和测试动作 (已注释)
# def servo_initialization_test():
#     """舵机初始化测试 - 验证舵机控制是否正常"""
#     print("开始舵机初始化测试...")

#     # MG996R 270°舵机参数 (临时定义，避免依赖后面的变量)
#     center_duty = 7.5   # 中位占空比 (1.5ms脉宽，对应0度)

#     # 1. 复位到中心位置
#     print("1. 舵机复位到中心位置...")
#     servo_horizontal.duty(center_duty)  # 7.5% (1.5ms)
#     servo_vertical.duty(center_duty)    # 7.5% (1.5ms)
#     time.sleep_ms(1000)  # 等待1秒

#     # 2. 水平舵机测试 (A19 -> PWM7)
#     print("2. 测试水平舵机 (A19-PWM7) - 左转...")
#     servo_horizontal.duty(5.0)  # 向左转 (约-45度)
#     time.sleep_ms(800)

#     print("   水平舵机 (A19-PWM7) - 右转...")
#     servo_horizontal.duty(10.0)  # 向右转 (约+45度)
#     time.sleep_ms(800)

#     print("   水平舵机 (A19-PWM7) - 回中心...")
#     servo_horizontal.duty(center_duty)  # 回中心
#     time.sleep_ms(500)

#     # 3. 垂直舵机测试 (A18 -> PWM6)
#     print("3. 测试垂直舵机 (A18-PWM6) - 上转...")
#     servo_vertical.duty(10.0)  # 向上转 (约+45度)
#     time.sleep_ms(800)

#     print("   垂直舵机 (A18-PWM6) - 下转...")
#     servo_vertical.duty(5.0)   # 向下转 (约-45度)
#     time.sleep_ms(800)

#     print("   垂直舵机 (A18-PWM6) - 回中心...")
#     servo_vertical.duty(center_duty)  # 回中心
#     time.sleep_ms(500)

#     print("✓ 舵机初始化测试完成！")
#     print("如果舵机没有按预期动作，请检查:")
#     print("  - 舵机电源连接 (5V)")
#     print("  - 引脚接线 (A19-PWM7=水平, A18-PWM6=垂直)")
#     print("  - 舵机是否损坏")
#     print("  - 舵机型号是否为MG996R 270°")
#     print("  - PWM通道配置是否正确")
#     print("-" * 50)

# # 执行舵机初始化测试 (已注释)
# servo_initialization_test()

# 摄像头和显示器初始化
cam = camera.Camera(320, 240)  # 提高分辨率以获得更好的识别效果
disp = display.Display()

# 白色A4纸的LAB色彩空间阈值（矩形中心的白色区域）
# 白色在LAB色彩空间中的特征：
# L: 高亮度 (50-100)
# A: 接近中性 (-20到20)
# B: 接近中性 (-20到20)
white_thresholds = [[50, 100, -20, 20, -20, 20]]

# 黑色胶带边框的LAB色彩空间阈值
# 黑色在LAB色彩空间中的特征：
# L: 低亮度 (0-30)
# A: 接近中性 (-15到15)
# B: 接近中性 (-15到15)
black_thresholds = [[0, 30, -15, 15, -15, 15]]

# 矩形检测参数（适应摄像头位置和距离变化）
# 方案：主要基于宽高比判断，辅以最小尺寸过滤

# A4纸宽高比检查（A4纸的宽高比约为 210:297 ≈ 0.71）
target_aspect_ratio = 210.0 / 297.0   # A4纸标准宽高比 ≈ 0.707
aspect_ratio_tolerance = 0.15          # 宽高比容忍度（±0.15）
min_aspect_ratio = target_aspect_ratio - aspect_ratio_tolerance  # ≈ 0.56
max_aspect_ratio = target_aspect_ratio + aspect_ratio_tolerance  # ≈ 0.86

# 最小尺寸过滤（防止检测到太小的噪声）
min_width = 15        # 最小宽度（像素）
min_height = 20       # 最小高度（像素）
min_area = 300        # 最小面积（像素²）
min_pixels = 150      # 最小像素数

# 最大尺寸限制（防止检测到整个画面）
max_width = 300       # 最大宽度（像素）
max_height = 400      # 最大高度（像素）

# 连续识别验证参数
required_detections = 2                # 需要连续识别成功的次数
coordinate_tolerance = 10              # 坐标变化容忍度（像素）
detection_count = 0                    # 连续检测计数器
last_center = None                     # 上次检测的中心点

# 帧率控制参数
target_fps = 15                        # 目标帧率（降低以减少闪烁）
frame_time = 1000 // target_fps        # 每帧间隔时间（毫秒）

# MG996R 270°舵机控制参数 (已注释)
# # 脉宽范围：0.5ms - 2.5ms，对应角度：-135° - +135°
# servo_min_duty = 2.5    # 舵机最小占空比 (0.5ms脉宽，对应-135度)
# servo_max_duty = 12.5   # 舵机最大占空比 (2.5ms脉宽，对应+135度)
# servo_center_duty = 7.5 # 舵机中位占空比 (1.5ms脉宽，对应0度)

# # 图像坐标到舵机角度的映射参数
# image_width = 320       # 图像宽度
# image_height = 240      # 图像高度
# image_center_x = image_width // 2   # 图像中心X坐标 (160)
# image_center_y = image_height // 2  # 图像中心Y坐标 (120)

# # MG996R 270°舵机角度范围
# servo_max_angle = 135   # 最大角度 +135度
# servo_min_angle = -135  # 最小角度 -135度
# servo_angle_range = 270 # 总角度范围 270度

# # 实际使用的角度范围（可以限制在较小范围内避免机械限位）
# use_angle_range = 90    # 实际使用±90度范围（可根据需要调整）

# 增量式PID控制器（输出角度增量，实现平滑控制）(已注释)
# class IncrementalPID:
#     """增量式PID控制器，输出角度增量而非绝对位置"""
#     def __init__(self, kp=0.05, ki=0.001, kd=0.02, max_increment=3.0):
#         self.kp = kp                    # 比例系数
#         self.ki = ki                    # 积分系数
#         self.kd = kd                    # 微分系数
#         self.max_increment = max_increment  # 最大增量限制（度）

#         # 内部状态变量
#         self.last_error = 0             # 上次误差
#         self.last_last_error = 0        # 上上次误差
#         self.last_time = 0              # 上次计算时间

#     def compute(self, error):
#         """计算PID输出增量（增量式）"""
#         current_time = time.ticks_ms()

#         # 计算时间间隔
#         if self.last_time == 0:
#             dt = 0.02  # 初始时间间隔，假设50Hz
#         else:
#             dt = time.ticks_diff(current_time, self.last_time) / 1000.0  # 转换为秒
#             if dt <= 0:
#                 dt = 0.02

#         self.last_time = current_time

#         # 增量式PID公式：
#         # Δu(k) = Kp[e(k) - e(k-1)] + Ki*e(k) + Kd[e(k) - 2*e(k-1) + e(k-2)]

#         # 比例增量
#         proportional_increment = self.kp * (error - self.last_error)

#         # 积分增量
#         integral_increment = self.ki * error * dt

#         # 微分增量
#         derivative_increment = self.kd * (error - 2 * self.last_error + self.last_last_error) / dt

#         # 总增量
#         increment = proportional_increment + integral_increment + derivative_increment

#         # 限制增量范围，避免过大的跳跃
#         increment = max(-self.max_increment, min(self.max_increment, increment))

#         # 更新历史误差
#         self.last_last_error = self.last_error
#         self.last_error = error

#         return increment

#     def reset(self):
#         """重置PID控制器状态"""
#         self.last_error = 0
#         self.last_last_error = 0
#         self.last_time = 0

# # 创建增量式PID控制器实例 (已注释)
# # 水平舵机PID：像素误差 -> 角度增量
# pid_horizontal = IncrementalPID(kp=0.08, ki=0.002, kd=0.03, max_increment=2.5)

# # 垂直舵机PID：像素误差 -> 角度增量（摄像头在此轴上，参数更保守）
# pid_vertical = IncrementalPID(kp=0.06, ki=0.001, kd=0.025, max_increment=2.0)

# # 舵机平滑移动控制参数 (已注释)
# current_horizontal_angle = 0.0    # 当前水平角度
# current_vertical_angle = 0.0      # 当前垂直角度
# max_angle_step = 2.0              # 每次最大角度变化（度）
# servo_move_delay = 30             # 舵机移动间隔时间（毫秒）

def calculate_rectangle_corners(blob):
    """根据blob计算矩形的四个角点"""
    x, y, w, h = blob[0], blob[1], blob[2], blob[3]

    corners = [
        [x, y],           # 左上角
        [x + w, y],       # 右上角
        [x + w, y + h],   # 右下角
        [x, y + h]        # 左下角
    ]

    return corners

# 舵机控制相关函数 (已注释)
# def angle_to_duty_cycle(angle):
#     """将角度转换为PWM占空比 (MG996R 270°舵机)"""
#     # 限制角度范围在 -135 到 +135 度之间
#     angle = max(servo_min_angle, min(servo_max_angle, angle))

#     # 线性映射：-135度 -> 2.5%, 0度 -> 7.5%, +135度 -> 12.5%
#     # 占空比 = 中位占空比 + (角度/最大角度) * (最大占空比 - 中位占空比)
#     if angle >= 0:
#         duty = servo_center_duty + (angle / servo_max_angle) * (servo_max_duty - servo_center_duty)
#     else:
#         duty = servo_center_duty + (angle / abs(servo_min_angle)) * (servo_center_duty - servo_min_duty)

#     return duty

# def coordinate_to_angle(coord, image_center, image_size):
#     """将图像坐标转换为舵机角度 (限制在实用范围内)"""
#     # 计算相对于图像中心的偏移
#     offset = coord - image_center

#     # 将偏移映射到实用角度范围 (±90度，而不是全部270度)
#     # 图像边缘对应最大实用角度
#     max_offset = image_size // 2
#     angle = (offset / max_offset) * use_angle_range

#     # 限制角度范围在实用范围内
#     angle = max(-use_angle_range, min(use_angle_range, angle))

#     return angle

# def control_servos_with_incremental_pid(center_x, center_y):
#     """使用增量式PID控制器实现平滑的舵机跟踪"""
#     global current_horizontal_angle, current_vertical_angle

#     # 计算目标偏离图像中心的误差（像素）
#     horizontal_error = center_x - image_center_x  # 正值表示目标在右侧
#     vertical_error = center_y - image_center_y    # 正值表示目标在下方

#     # 使用增量式PID计算角度增量
#     # 水平：目标在右侧时，舵机应该向右转（正增量）
#     horizontal_increment = pid_horizontal.compute(horizontal_error)

#     # 垂直：目标在下方时，舵机应该向下转（负增量，因为摄像头向下看）
#     vertical_increment = pid_vertical.compute(-vertical_error)  # 负号反转Y轴

#     # 更新当前角度（累积增量）
#     current_horizontal_angle += horizontal_increment
#     current_vertical_angle += vertical_increment

#     # 限制角度范围，避免超出机械限位
#     current_horizontal_angle = max(-use_angle_range, min(use_angle_range, current_horizontal_angle))
#     current_vertical_angle = max(-use_angle_range, min(use_angle_range, current_vertical_angle))

#     # 转换为PWM占空比
#     horizontal_duty = angle_to_duty_cycle(current_horizontal_angle)
#     vertical_duty = angle_to_duty_cycle(current_vertical_angle)

#     # 控制舵机
#     servo_horizontal.duty(horizontal_duty)
#     servo_vertical.duty(vertical_duty)

#     # 计算对应的脉宽 (用于显示)
#     horizontal_pulse_width = (horizontal_duty / 100) * 20  # ms
#     vertical_pulse_width = (vertical_duty / 100) * 20      # ms

#     print(f"增量式PID: 误差({horizontal_error:.0f},{vertical_error:.0f})px "
#           f"增量({horizontal_increment:.2f}°,{vertical_increment:.2f}°) "
#           f"当前角度({current_horizontal_angle:.1f}°,{current_vertical_angle:.1f}°)")

#     return current_horizontal_angle, current_vertical_angle

# def reset_servos_to_center():
#     """将舵机复位到中心位置并重置增量式PID状态"""
#     global current_horizontal_angle, current_vertical_angle

#     # 重置PID控制器状态
#     pid_horizontal.reset()
#     pid_vertical.reset()

#     # 重置当前角度
#     current_horizontal_angle = 0.0
#     current_vertical_angle = 0.0

#     # 舵机直接回中心位置
#     servo_horizontal.duty(servo_center_duty)
#     servo_vertical.duty(servo_center_duty)

#     print("舵机已复位到中心位置，增量式PID状态已重置")

# def reset_servos_to_center():
#     """将舵机复位到中心位置"""
#     servo_horizontal.duty(servo_center_duty)
#     servo_vertical.duty(servo_center_duty)
#     print("舵机已复位到中心位置")

def find_white_rectangle():
    """使用find_blobs检测白色A4纸矩形"""
    # 在整个图像中寻找白色区域
    white_blobs = img.find_blobs(white_thresholds,
                                pixels_threshold=min_pixels,
                                area_threshold=min_area,
                                merge=True)

    if not white_blobs:
        return None

    # 找到最大的白色区域（假设这是A4纸）
    largest_white_blob = max(white_blobs, key=lambda b: b[2] * b[3])

    # 检查尺寸是否合理
    w, h = largest_white_blob[2], largest_white_blob[3]
    if w < min_width or h < min_height:
        return None

    return largest_white_blob

def is_valid_a4_rectangle(w, h):
    """检查矩形是否符合A4纸的宽高比要求（适应距离和角度变化）"""
    # 基本尺寸过滤（防止太小的噪声）
    if w < min_width or h < min_height:
        return False

    # 防止检测到整个画面
    if w > max_width or h > max_height:
        return False

    # 面积过滤
    area = w * h
    if area < min_area:
        return False

    # 核心判断：宽高比检查（A4纸应该是宽<高）
    aspect_ratio = w / h
    if aspect_ratio < min_aspect_ratio or aspect_ratio > max_aspect_ratio:
        return False

    # 额外检查：确保高度大于宽度（A4纸竖放）
    if h <= w:
        return False

    return True

def calculate_aspect_ratio_score(w, h):
    """计算宽高比与A4纸标准比例的匹配度（0-1，越接近1越好）"""
    if h == 0:
        return 0

    aspect_ratio = w / h
    diff = abs(aspect_ratio - target_aspect_ratio)
    max_diff = aspect_ratio_tolerance

    if diff > max_diff:
        return 0

    # 线性评分：差异越小分数越高
    score = 1.0 - (diff / max_diff)
    return score

def detect_rectangle_with_roi():
    """先检测黑色胶带边框，再在其内部寻找白色A4纸（基于宽高比判断）"""
    # 首先在整个图像中寻找黑色胶带边框
    black_blobs = img.find_blobs(black_thresholds,
                                pixels_threshold=min_pixels,
                                area_threshold=min_area,
                                merge=True)

    if not black_blobs:
        return None

    # 找到所有符合A4纸宽高比的黑色区域，并按匹配度排序
    valid_candidates = []

    for black_blob in black_blobs:
        black_x, black_y, black_w, black_h = black_blob[0], black_blob[1], black_blob[2], black_blob[3]

        # 检查黑色边框是否符合A4纸宽高比要求
        if not is_valid_a4_rectangle(black_w, black_h):
            continue

        # 计算宽高比匹配度
        score = calculate_aspect_ratio_score(black_w, black_h)
        valid_candidates.append((black_blob, score))

    # 按匹配度排序，优先检测最符合A4纸比例的区域
    valid_candidates.sort(key=lambda x: x[1], reverse=True)

    # 遍历候选区域
    for black_blob, score in valid_candidates:
        black_x, black_y, black_w, black_h = black_blob[0], black_blob[1], black_blob[2], black_blob[3]

        # 在黑色边框内部设置ROI来寻找白色A4纸
        # 动态计算边框宽度（根据矩形大小调整）
        margin = max(2, min(black_w // 25, black_h // 25, 6))
        roi_x = black_x + margin
        roi_y = black_y + margin
        roi_w = black_w - 2 * margin
        roi_h = black_h - 2 * margin

        # 确保ROI有效
        if roi_w <= 5 or roi_h <= 5:
            continue

        roi = [roi_x, roi_y, roi_w, roi_h]

        # 在黑色边框内部寻找白色A4纸
        white_blobs = img.find_blobs(white_thresholds,
                                    roi=roi,
                                    pixels_threshold=min_pixels // 6,
                                    area_threshold=min_area // 6,
                                    merge=True)

        # 如果在黑色边框内找到白色区域
        if white_blobs:
            # 找到最大的白色区域
            largest_white_blob = max(white_blobs, key=lambda b: b[2] * b[3])

            # 验证白色区域的宽高比
            white_w, white_h = largest_white_blob[2], largest_white_blob[3]
            if is_valid_a4_rectangle(white_w, white_h):
                print(f"找到A4纸候选: 黑框({black_w}×{black_h}) 白纸({white_w}×{white_h}) 匹配度:{score:.2f}")
                return largest_white_blob

    return None

# ========== 串口数据包相关函数 (解除注释) ==========
def calculate_checksum(data):
    """计算数据的校验和（简单累加取低8位）"""
    return sum(data) & 0xFF

def create_data_packet(center_x, center_y):
    """
    创建标准数据包格式
    格式: [包头AA 55] [长度] [数据内容] [校验和] [包尾0D 0A]
    数据内容: [中心X低字节] [中心X高字节] [中心Y低字节] [中心Y高字节]
    """
    # 包头
    header = [0xAA, 0x55]

    # 数据内容（4字节：X坐标2字节 + Y坐标2字节）
    data_content = [
        center_x & 0xFF,        # X坐标低字节
        (center_x >> 8) & 0xFF, # X坐标高字节
        center_y & 0xFF,        # Y坐标低字节
        (center_y >> 8) & 0xFF  # Y坐标高字节
    ]

    # 数据长度（数据内容的字节数）
    data_length = len(data_content)

    # 计算校验和（对数据长度和数据内容进行校验）
    checksum_data = [data_length] + data_content
    checksum = calculate_checksum(checksum_data)

    # 包尾
    tail = [0x0D, 0x0A]

    # 组装完整数据包
    packet = header + [data_length] + data_content + [checksum] + tail

    return bytes(packet)
# ========== 串口数据包相关函数结束 ==========

def is_same_detection(center_x, center_y):
    """检查当前检测是否与上次检测相同（在容忍范围内）"""
    global last_center

    if last_center is None:
        return False

    diff_x = abs(center_x - last_center[0])
    diff_y = abs(center_y - last_center[1])

    return diff_x <= coordinate_tolerance and diff_y <= coordinate_tolerance

def detect_and_send_rectangle():
    """检测A4纸矩形并在连续识别2次相同坐标后发送"""
    global detection_count, last_center

    # 使用ROI检测黑色边框来验证矩形
    rectangle_blob = detect_rectangle_with_roi()

    if rectangle_blob is not None:
        x, y, w, h = rectangle_blob[0], rectangle_blob[1], rectangle_blob[2], rectangle_blob[3]

        # 计算中心点
        center_x = x + w // 2
        center_y = y + h // 2

        # 计算四个角点
        corners = calculate_rectangle_corners(rectangle_blob)

        # 绘制矩形边框
        img.draw_rect(x, y, w, h, color=image.COLOR_GREEN, thickness=2)

        # 绘制中心点
        img.draw_cross(center_x, center_y, color=image.COLOR_RED, size=8)

        # 绘制四个角点
        for corner in corners:
            img.draw_circle(corner[0], corner[1], 3, color=image.COLOR_BLUE, thickness=2)

        # 显示矩形信息
        aspect_ratio = w / h
        print(f"检测到A4纸: 尺寸({w}×{h}) 宽高比({aspect_ratio:.2f}) 中心({center_x},{center_y})")

        # 检查是否与上次检测相同
        if is_same_detection(center_x, center_y):
            detection_count += 1
            print(f"连续检测次数: {detection_count}/{required_detections}")
        else:
            detection_count = 1  # 重置计数器
            print(f"新位置检测，重新计数: {detection_count}/{required_detections}")

        # 更新上次检测结果
        last_center = (center_x, center_y)

        # 只有连续检测到相同位置达到要求次数才发送串口数据
        if detection_count >= required_detections:
            try:
                # 构建数据包 (串口发送解除注释)
                packet = create_data_packet(center_x, center_y)
                serial.write(packet)
                print(f"✓ A4纸中心点: ({center_x}, {center_y}) - 数据包已发送")
                print(f"  数据包: {' '.join([f'{b:02X}' for b in packet])}")

            except Exception as e:
                print(f"串口发送失败: {e}")

            # 使用增量式PID控制舵机跟踪目标 (已注释)
            # try:
            #     horizontal_angle, vertical_angle = control_servos_with_incremental_pid(center_x, center_y)
            #     print(f"✓ A4纸中心点: ({center_x}, {center_y}) - 增量式PID舵机跟踪中")
            # except Exception as e:
            #     print(f"增量式PID舵机控制失败: {e}")
        else:
            print(f"等待连续检测 {required_detections} 次相同位置...")

        return True
    else:
        # 没有检测到矩形，重置计数器
        if detection_count > 0:
            detection_count = 0
            last_center = None
            print("未检测到A4纸，重置计数器")
            # 舵机回到中心位置 (已注释)
            # try:
            #     reset_servos_to_center()
            # except Exception as e:
            #     print(f"舵机复位失败: {e}")
        return False

# 主程序
print("MaixCam Pro A4纸矩形检测与串口坐标传送程序")
print("适应摄像头位置和距离变化 + 串口数据传输")
print("=" * 65)
print("检测策略：")
print("✓ 主要基于A4纸宽高比判断（210:297 ≈ 0.71）")
print("✓ 适应摄像头前后、左右位置变化")
print("✓ 适应不同距离的矩形大小变化")
print("✓ 连续识别验证机制")
print("=" * 60)
# 舵机控制信息 (已注释)
# print("MG996R 270°舵机增量式PID控制：")
# print("✓ A19引脚 -> PWM7通道 -> 水平舵机 (左右转动)")
# print("✓ A18引脚 -> PWM6通道 -> 垂直舵机 (上下转动，摄像头安装)")
# print("✓ PWM频率: 50Hz")
# print("✓ 脉宽范围: 0.5ms-2.5ms (占空比2.5%-12.5%)")
# print("✓ 舵机角度范围: ±135° (270°总范围)")
# print(f"✓ 实际使用角度: ±{use_angle_range}° (避免机械限位)")
# print("✓ 增量式PID控制，输出角度增量而非绝对位置")
# print("✓ 平滑累积调整，避免突然跳跃")
# print("✓ 防止摄像头快速移动导致图像模糊")
# print("✓ 目标丢失时直接回中心位置并重置PID状态")
print("串口数据传输：")
print("✓ 串口设备: /dev/ttyS0")
print("✓ 波特率: 115200")
print("✓ 数据包格式: [包头AA 55] [长度] [数据内容] [校验和] [包尾0D 0A]")
print("✓ 坐标数据: X坐标2字节 + Y坐标2字节")
print("=" * 65)
print("A4纸宽高比检测：")
print(f"标准宽高比: {target_aspect_ratio:.3f}")
print(f"容忍范围: {min_aspect_ratio:.2f} - {max_aspect_ratio:.2f}")
print(f"容忍度: ±{aspect_ratio_tolerance:.2f}")
print("=" * 55)
print("尺寸过滤参数：")
print(f"最小尺寸: {min_width}×{min_height} 像素")
print(f"最大尺寸: {max_width}×{max_height} 像素")
print(f"最小面积: {min_area} 像素²")
print(f"坐标容忍度: ±{coordinate_tolerance} 像素")
print(f"连续检测要求: {required_detections} 次")
print("=" * 55)
# 增量式PID控制参数 (已注释)
# print("增量式PID控制参数：")
# print(f"水平舵机PID: Kp={pid_horizontal.kp}, Ki={pid_horizontal.ki}, Kd={pid_horizontal.kd}")
# print(f"垂直舵机PID: Kp={pid_vertical.kp}, Ki={pid_vertical.ki}, Kd={pid_vertical.kd}")
# print(f"最大增量限制: 水平±{pid_horizontal.max_increment}°, 垂直±{pid_vertical.max_increment}°")
# print("控制原理: 像素误差 -> PID计算 -> 角度增量 -> 累积到当前角度")
print("=" * 65)
print("串口数据传输流程：")
print("1. 检测黑色胶带边框")
print("2. 按宽高比匹配度排序")
print("3. 在最佳候选区域内寻找白色A4纸")
print("4. 验证白色区域宽高比")
print("5. 连续识别2次相同坐标后发送串口数据")
print("6. 计算A4纸中心点坐标")
print("7. 构建标准数据包格式")
print("8. 通过串口发送坐标数据")
print("9. 目标丢失时停止发送数据")
print("=" * 65)
print("LAB色彩空间阈值：")
print("黑色胶带: L(0-30) A(-15到15) B(-15到15)")
print("白色A4纸: L(50-100) A(-20到20) B(-20到20)")
print("=" * 55)
print("显示控制：")
print(f"目标帧率: {target_fps} FPS（防止屏幕闪烁）")
print(f"帧间隔: {frame_time} ms")
print("=" * 55)
# 坐标映射算法 (已注释)
# print("坐标映射算法 (MG996R 270°舵机)：")
# print("图像中心(160,120) -> 舵机角度(0°,0°) -> 脉宽(1.5ms,1.5ms)")
# print(f"图像边缘 -> 舵机最大角度(±{use_angle_range}°)")
# print("X坐标 -> 水平舵机角度 (左负右正)")
# print("Y坐标 -> 垂直舵机角度 (上正下负)")
# print("脉宽计算: 角度 -> 占空比 -> 脉宽(0.5ms-2.5ms)")
print("串口数据包格式：")
print("包头: AA 55 (2字节)")
print("数据长度: 04 (1字节，表示4字节数据)")
print("X坐标: 低字节 + 高字节 (2字节)")
print("Y坐标: 低字节 + 高字节 (2字节)")
print("校验和: 数据长度和数据内容的累加和 (1字节)")
print("包尾: 0D 0A (2字节)")
print("=" * 60)
print("注意: 舵机功能已注释，现在使用串口传输坐标")
print("=" * 60)

while not app.need_exit():
    try:
        frame_start = time.ticks_ms()  # 记录帧开始时间

        img = cam.read()  # 获取摄像头图像

        # 检测矩形并发送坐标
        detect_and_send_rectangle()

        disp.show(img)  # 显示图像

        # 帧率控制：确保每帧间隔不少于指定时间
        frame_end = time.ticks_ms()
        frame_duration = time.ticks_diff(frame_end, frame_start)

        if frame_duration < frame_time:
            sleep_time = frame_time - frame_duration
            time.sleep_ms(sleep_time)

    except KeyboardInterrupt:
        print("程序被用户中断")
        break
    except Exception as e:
        print(f"程序运行错误: {e}")
        time.sleep_ms(100)  # 出错时也要延时，避免快速循环
        continue

print("程序结束")
