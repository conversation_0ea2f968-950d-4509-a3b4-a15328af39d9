from maix import image, camera, display, app, time, uart

# ==================== 可调参数区域 ====================
# 连续识别参数
REQUIRED_SUCCESS = 2       # 需要连续成功的次数
COORDINATE_TOLERANCE = 5   # 坐标变化容忍度

# 检测范围参数（针对空心矩形优化）
MIN_WIDTH = 15             # 最小宽度（空心矩形需要一定尺寸）
MAX_WIDTH = 140            # 最大宽度
MIN_HEIGHT = 12            # 最小高度
MAX_HEIGHT = 100           # 最大高度

# 黑色边框阈值 (LAB色彩空间) - 针对空心矩形优化
BLACK_L_MIN = 0            # L通道最小值 (亮度)
BLACK_L_MAX = 25           # L通道最大值 (调高以适应不同光照)
BLACK_A_MIN = -20          # A通道最小值 (绿-红)
BLACK_A_MAX = 20           # A通道最大值
BLACK_B_MIN = -20          # B通道最小值 (蓝-黄)
BLACK_B_MAX = 20           # B通道最大值

# 检测阈值参数
RECT_THRESHOLD = 9000      # 矩形检测阈值
MIN_PIXELS = 60            # 最小像素数（空心矩形边框像素较多）
MIN_AREA = 80              # 最小面积
MAX_AREA = 6000            # 最大面积

# 线段检测参数（用于空心矩形）
LINE_MERGE_DISTANCE = 8    # 线段合并距离
LINE_MAX_THETA_DIFF = 25   # 最大角度差异
MIN_LINE_LENGTH = 10       # 最小线段长度
# ==================== 参数区域结束 ====================

# 串口配置
device = "/dev/ttyS0"
serial = uart.UART(device, 115200)

# 摄像头和显示器初始化
cam = camera.Camera(160, 120)
disp = display.Display()

# 根据参数构建阈值
black_threshold = [[BLACK_L_MIN, BLACK_L_MAX, BLACK_A_MIN, BLACK_A_MAX, BLACK_B_MIN, BLACK_B_MAX]]

# 连续识别状态变量
success_count = 0
last_detection = None

def is_detection_stable(current_detection):
    """检查检测结果稳定性"""
    global last_detection
    
    if last_detection is None:
        return False
    
    center_diff_x = abs(current_detection['center_x'] - last_detection['center_x'])
    center_diff_y = abs(current_detection['center_y'] - last_detection['center_y'])
    size_diff_w = abs(current_detection['width'] - last_detection['width'])
    size_diff_h = abs(current_detection['height'] - last_detection['height'])
    
    return (center_diff_x <= COORDINATE_TOLERANCE and 
            center_diff_y <= COORDINATE_TOLERANCE and
            size_diff_w <= COORDINATE_TOLERANCE and 
            size_diff_h <= COORDINATE_TOLERANCE)

def detect_by_lines(roi=None):
    """通过线段检测识别空心矩形"""
    try:
        lines = img.find_line_segments(roi=roi, 
                                     merge_distance=LINE_MERGE_DISTANCE, 
                                     max_theta_difference=LINE_MAX_THETA_DIFF)
        
        if len(lines) >= 3:  # 至少需要3条线段
            # 过滤短线段
            valid_lines = [line for line in lines if line.length() >= MIN_LINE_LENGTH]
            
            if len(valid_lines) >= 3:
                # 按长度排序
                valid_lines.sort(key=lambda line: line.length(), reverse=True)
                main_lines = valid_lines[:4]
                
                # 计算边界框
                min_x = min([min(line.x1(), line.x2()) for line in main_lines])
                max_x = max([max(line.x1(), line.x2()) for line in main_lines])
                min_y = min([min(line.y1(), line.y2()) for line in main_lines])
                max_y = max([max(line.y1(), line.y2()) for line in main_lines])
                
                rect_w = max_x - min_x
                rect_h = max_y - min_y
                
                # 验证尺寸
                if (rect_w >= MIN_WIDTH and rect_w <= MAX_WIDTH and
                    rect_h >= MIN_HEIGHT and rect_h <= MAX_HEIGHT):
                    
                    return [min_x, min_y, rect_w, rect_h], [
                        [min_x, min_y + rect_h],           # 左下角
                        [min_x + rect_w, min_y + rect_h],  # 右下角
                        [min_x + rect_w, min_y],           # 右上角
                        [min_x, min_y]                     # 左上角
                    ]
    except Exception as e:
        print(f"线段检测错误: {e}")
    
    return None, None

def detect_by_contour():
    """通过轮廓检测识别空心矩形"""
    try:
        # 检测黑色区域（边框）
        black_blobs = img.find_blobs(black_threshold,
                                   pixels_threshold=MIN_PIXELS,
                                   area_threshold=MIN_AREA,
                                   merge=False)  # 不合并，保持边框结构
        
        if black_blobs:
            valid_blobs = []
            for blob in black_blobs:
                blob_w, blob_h = blob[2], blob[3]
                blob_area = blob_w * blob_h
                
                # 空心矩形的特征：周长相对面积较大
                perimeter = 2 * (blob_w + blob_h)
                if blob_area > 0:
                    perimeter_area_ratio = perimeter / blob_area
                    
                    # 空心矩形的周长面积比应该较大
                    if (blob_w >= MIN_WIDTH and blob_w <= MAX_WIDTH and
                        blob_h >= MIN_HEIGHT and blob_h <= MAX_HEIGHT and
                        blob_area >= MIN_AREA and blob_area <= MAX_AREA and
                        perimeter_area_ratio > 0.3):  # 空心特征
                        valid_blobs.append(blob)
            
            if valid_blobs:
                # 选择最符合条件的blob
                best_blob = max(valid_blobs, key=lambda b: b[2] * b[3])
                
                blob_x, blob_y, blob_w, blob_h = best_blob[0], best_blob[1], best_blob[2], best_blob[3]
                
                return [blob_x, blob_y, blob_w, blob_h], [
                    [blob_x, blob_y + blob_h],           # 左下角
                    [blob_x + blob_w, blob_y + blob_h],  # 右下角
                    [blob_x + blob_w, blob_y],           # 右上角
                    [blob_x, blob_y]                     # 左上角
                ]
    except Exception as e:
        print(f"轮廓检测错误: {e}")
    
    return None, None

def detect_hollow_rectangle():
    """检测黑色空心矩形"""
    global success_count, last_detection
    
    detection_found = False
    
    # 方法1：直接使用标准矩形检测
    rect_found = False
    try:
        rects = img.find_rects(threshold=RECT_THRESHOLD)
        if rects:
            for rect in rects:
                x3, y3, w3, h3 = rect[0], rect[1], rect[2], rect[3]
                
                if (w3 >= MIN_WIDTH and w3 <= MAX_WIDTH and
                    h3 >= MIN_HEIGHT and h3 <= MAX_HEIGHT):
                    
                    rect_corners = rect.corners()
                    rect_found = True
                    print("✓ 使用标准矩形检测")
                    break
    except Exception as e:
        print(f"标准矩形检测失败: {e}")
    
    # 方法2：线段检测
    if not rect_found:
        pseudo_rect, corners = detect_by_lines()
        if pseudo_rect:
            x3, y3, w3, h3 = pseudo_rect
            rect_corners = corners
            rect_found = True
            print("✓ 使用线段检测")
    
    # 方法3：轮廓检测
    if not rect_found:
        pseudo_rect, corners = detect_by_contour()
        if pseudo_rect:
            x3, y3, w3, h3 = pseudo_rect
            rect_corners = corners
            rect_found = True
            print("✓ 使用轮廓检测")
    
    # 处理检测结果
    if rect_found and rect_corners:
        center_x = x3 + w3 // 2
        center_y = y3 + h3 // 2
        
        # 绘制检测结果
        img.draw_cross(center_x, center_y, color=image.COLOR_RED, size=5)
        
        point_size = max(2, min(6, w3 // 20))
        for corner in rect_corners:
            img.draw_circle(corner[0], corner[1], point_size, 
                          color=image.COLOR_WHITE, thickness=2)
        
        # 提取角点坐标
        top_left = [rect_corners[3][0], rect_corners[3][1]]
        top_right = [rect_corners[2][0], rect_corners[2][1]]
        bottom_right = [rect_corners[1][0], rect_corners[1][1]]
        bottom_left = [rect_corners[0][0], rect_corners[0][1]]
        
        # 创建检测结果
        current_detection = {
            'center_x': center_x,
            'center_y': center_y,
            'width': w3,
            'height': h3,
            'top_left': top_left,
            'top_right': top_right,
            'bottom_right': bottom_right,
            'bottom_left': bottom_left
        }
        
        # 稳定性检查
        if is_detection_stable(current_detection):
            success_count += 1
            print(f"稳定检测 {success_count}/{REQUIRED_SUCCESS} - 中心({center_x},{center_y}) 尺寸({w3}x{h3})")
        else:
            success_count = 1
            print(f"新检测 {success_count}/{REQUIRED_SUCCESS} - 中心({center_x},{center_y}) 尺寸({w3}x{h3})")
        
        last_detection = current_detection
        detection_found = True
        
        # 发送坐标
        if success_count >= REQUIRED_SUCCESS:
            data = [0x13, 0x23, center_x, center_y,
                   top_left[0], top_left[1],
                   top_right[0], top_right[1],
                   bottom_right[0], bottom_right[1],
                   bottom_left[0], bottom_left[1]]
            
            try:
                serial.write(bytes(data))
                print(f"✓ 空心矩形坐标已发送: 中心({center_x},{center_y})")
            except Exception as e:
                print(f"✗ 发送失败: {e}")
    
    # 重置状态
    if not detection_found:
        if last_detection is not None:
            success_count = 0
            last_detection = None
            print("检测丢失，重置计数器")

# 主程序
print("黑色空心矩形识别程序")
print("=" * 60)
print("专门用于识别白色背景上的黑色空心矩形")
print("=" * 60)
print("当前参数配置:")
print(f"  连续成功要求: {REQUIRED_SUCCESS} 次")
print(f"  坐标容忍度: ±{COORDINATE_TOLERANCE} 像素")
print(f"  尺寸范围: {MIN_WIDTH}x{MIN_HEIGHT} 到 {MAX_WIDTH}x{MAX_HEIGHT}")
print(f"  黑色阈值: L({BLACK_L_MIN}-{BLACK_L_MAX}) A({BLACK_A_MIN}-{BLACK_A_MAX}) B({BLACK_B_MIN}-{BLACK_B_MAX})")
print(f"  矩形检测阈值: {RECT_THRESHOLD}")
print("=" * 60)
print("检测策略:")
print("1. 标准矩形检测（优先）")
print("2. 线段检测（适用于不完整边框）")
print("3. 轮廓检测（适用于复杂情况）")
print("=" * 60)
print("光照建议:")
print("- 确保均匀照明，避免强烈阴影")
print("- 黑色边框与白色背景对比度要足够")
print("- 避免反光和过度曝光")
print("=" * 60)

while not app.need_exit():
    try:
        img = cam.read()
        detect_hollow_rectangle()
        disp.show(img)
        
    except KeyboardInterrupt:
        print("程序被用户中断")
        break
    except Exception as e:
        print(f"程序运行错误: {e}")
        success_count = 0
        last_detection = None
        continue

print("程序结束")
