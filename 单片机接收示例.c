/*
 * MaixCam Pro A4纸坐标数据包接收示例
 * 适用于STM32、Arduino等单片机平台
 * 
 * 数据包格式（9字节）：
 * [AA 55] [长度] [X低 X高 Y低 Y高] [校验和] [0D 0A]
 */

#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>

// 数据包定义
#define PACKET_HEADER1      0xAA
#define PACKET_HEADER2      0x55
#define PACKET_TAIL1        0x0D
#define PACKET_TAIL2        0x0A
#define DATA_LENGTH         4       // X坐标2字节 + Y坐标2字节
#define PACKET_TOTAL_LENGTH 9       // 完整数据包长度

// 接收状态机
typedef enum {
    WAIT_HEADER1,       // 等待包头第一个字节 0xAA
    WAIT_HEADER2,       // 等待包头第二个字节 0x55
    WAIT_LENGTH,        // 等待数据长度字节
    WAIT_DATA,          // 等待数据内容
    WAIT_CHECKSUM,      // 等待校验和
    WAIT_TAIL1,         // 等待包尾第一个字节 0x0D
    WAIT_TAIL2          // 等待包尾第二个字节 0x0A
} PacketState;

// 接收缓冲区和状态变量
static uint8_t rx_buffer[16];
static PacketState rx_state = WAIT_HEADER1;
static uint8_t rx_index = 0;
static uint8_t data_length = 0;
static uint8_t received_checksum = 0;

// 坐标数据结构
typedef struct {
    uint16_t center_x;
    uint16_t center_y;
    bool valid;
} CoordinateData;

// 计算校验和
uint8_t calculate_checksum(uint8_t *data, uint8_t length) {
    uint8_t sum = 0;
    for (uint8_t i = 0; i < length; i++) {
        sum += data[i];
    }
    return sum & 0xFF;
}

// 解析坐标数据
CoordinateData parse_coordinate_data(uint8_t *data) {
    CoordinateData coord;
    
    // 解析X坐标（小端序：低字节在前）
    coord.center_x = data[0] | (data[1] << 8);
    
    // 解析Y坐标（小端序：低字节在前）
    coord.center_y = data[2] | (data[3] << 8);
    
    coord.valid = true;
    return coord;
}

// 处理接收到的坐标数据
void handle_coordinate_data(uint16_t center_x, uint16_t center_y) {
    printf("收到A4纸中心坐标: X=%d, Y=%d\n", center_x, center_y);
    
    // 在这里添加您的舵机控制代码
    // 例如：
    // servo_move_to_position(center_x, center_y);
    // 或者计算舵机角度：
    // float angle_x = map_coordinate_to_angle(center_x, 0, 320, -90, 90);
    // float angle_y = map_coordinate_to_angle(center_y, 0, 240, -60, 60);
    // servo_set_angle(SERVO_X, angle_x);
    // servo_set_angle(SERVO_Y, angle_y);
}

// 数据包接收处理函数（在串口中断或主循环中调用）
bool process_received_byte(uint8_t byte) {
    switch (rx_state) {
        case WAIT_HEADER1:
            if (byte == PACKET_HEADER1) {
                rx_state = WAIT_HEADER2;
                rx_index = 0;
            }
            break;
            
        case WAIT_HEADER2:
            if (byte == PACKET_HEADER2) {
                rx_state = WAIT_LENGTH;
            } else {
                rx_state = WAIT_HEADER1;  // 重新开始
            }
            break;
            
        case WAIT_LENGTH:
            if (byte == DATA_LENGTH) {  // 检查数据长度是否正确
                data_length = byte;
                rx_state = WAIT_DATA;
                rx_index = 0;
            } else {
                rx_state = WAIT_HEADER1;  // 数据长度错误，重新开始
            }
            break;
            
        case WAIT_DATA:
            rx_buffer[rx_index++] = byte;
            if (rx_index >= data_length) {
                rx_state = WAIT_CHECKSUM;
            }
            break;
            
        case WAIT_CHECKSUM:
            received_checksum = byte;
            rx_state = WAIT_TAIL1;
            break;
            
        case WAIT_TAIL1:
            if (byte == PACKET_TAIL1) {
                rx_state = WAIT_TAIL2;
            } else {
                rx_state = WAIT_HEADER1;  // 包尾错误，重新开始
            }
            break;
            
        case WAIT_TAIL2:
            if (byte == PACKET_TAIL2) {
                // 完整数据包接收完成，进行校验
                uint8_t checksum_data[5];  // 长度(1) + 数据(4)
                checksum_data[0] = data_length;
                for (int i = 0; i < data_length; i++) {
                    checksum_data[i + 1] = rx_buffer[i];
                }
                
                uint8_t calculated_checksum = calculate_checksum(checksum_data, 5);
                
                if (calculated_checksum == received_checksum) {
                    // 校验成功，解析坐标数据
                    CoordinateData coord = parse_coordinate_data(rx_buffer);
                    handle_coordinate_data(coord.center_x, coord.center_y);
                    rx_state = WAIT_HEADER1;
                    return true;  // 成功接收一个完整数据包
                } else {
                    printf("校验和错误: 计算值=0x%02X, 接收值=0x%02X\n", 
                           calculated_checksum, received_checksum);
                }
            }
            rx_state = WAIT_HEADER1;  // 重新开始
            break;
    }
    
    return false;  // 未完成数据包接收
}

// 串口中断服务函数示例（STM32 HAL库）
/*
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart) {
    if (huart->Instance == USART1) {
        uint8_t received_byte;
        HAL_UART_Receive(&huart1, &received_byte, 1, 0);
        process_received_byte(received_byte);
        
        // 重新启动接收
        HAL_UART_Receive_IT(&huart1, &received_byte, 1);
    }
}
*/

// Arduino示例
/*
void setup() {
    Serial.begin(115200);
    Serial.println("等待MaixCam Pro坐标数据...");
}

void loop() {
    if (Serial.available()) {
        uint8_t byte = Serial.read();
        if (process_received_byte(byte)) {
            // 成功接收到一个完整的坐标数据包
            // 可以在这里添加LED指示或其他反馈
        }
    }
}
*/

// 测试函数
void test_packet_parsing() {
    // 测试数据包: AA 55 04 A0 00 78 00 1F 0D 0A
    // 对应坐标: X=160(0x00A0), Y=120(0x0078)
    uint8_t test_packet[] = {0xAA, 0x55, 0x04, 0xA0, 0x00, 0x78, 0x00, 0x1F, 0x0D, 0x0A};
    
    printf("测试数据包解析:\n");
    for (int i = 0; i < sizeof(test_packet); i++) {
        printf("处理字节: 0x%02X\n", test_packet[i]);
        if (process_received_byte(test_packet[i])) {
            printf("成功解析完整数据包!\n");
            break;
        }
    }
}

int main() {
    printf("MaixCam Pro 坐标数据包接收测试\n");
    printf("数据包格式: [AA 55] [04] [X低 X高 Y低 Y高] [校验和] [0D 0A]\n\n");
    
    test_packet_parsing();
    
    return 0;
}
