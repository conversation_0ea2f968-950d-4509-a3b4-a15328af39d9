from maix import image, camera, display, app, time, uart  # 导入必要的模块

# pinmap.set_pin_function("A18", "UART1_RX")
# pinmap.set_pin_function("A19", "UART1_TX")

# device = "/dev/ttyS1"

device = "/dev/ttyS0"

serial = uart.UART(device, 115200)

cam = camera.Camera(160, 120)
disp = display.Display()

# 矩形检测的黑色边框阈值 (LAB色彩空间)
black_threshold = [[0, 14, -10, 10, -11, 9]]

# 宽度阈值设置
min_width_threshold = 20  # 最小宽度阈值
max_width_threshold = 200  # 最大宽度阈值

# FPS计算相关变量
fps_counter = 0
fps_start_time = 0
current_fps = 0



def juxingkuang():
    rect_corners=None
    black_blobs = img.find_blobs(black_threshold, pixels_threshold=200, area_threshold=200, merge=False)
    if black_blobs:
        for blob in black_blobs:
            # 宽度阈值检测
            if blob[2] < min_width_threshold or blob[2] > max_width_threshold:
                continue  # 跳过不符合宽度要求的矩形

            # 设置ROI区域
            roi1=[blob[0]-8, blob[1]-8, blob[2]+16, blob[3]+16]

            if blob[2]<= 155 and blob[3]<=115:
                rects = img.find_rects(roi=roi1,threshold=15000)#threshold参数很重要
                for rect in rects:#找出矩形
                    x3, y3, w3, h3 = rect[0], rect[1], rect[2], rect[3]

                    # 再次检查矩形宽度
                    if w3 < min_width_threshold or w3 > max_width_threshold:
                        continue

                    # 绘制矩形中心点
                    img.draw_cross(x3 + w3 // 2, y3 + h3 // 2, color=image.COLOR_RED)
                    center_x = x3+w3//2
                    center_y = y3+h3//2
                    print("矩形的中心坐标为：",center_x, center_y)
                    print("矩形宽度：", w3, "高度：", h3)

                    rect_corners = rect.corners()
                    for corner in rect_corners:  #画出矩形的角点
                        img.draw_circle(corner[0], corner[1], 3, color=image.COLOR_WHITE, thickness=1)

                    if rect_corners is not None:
                        # 初始化新的角点坐标存储变量
                        top_left = [0, 0]
                        top_right = [0, 0]
                        bottom_right = [0, 0]
                        bottom_left = [0, 0]
                        top_left[0] = rect_corners[3][0]   #左上角坐标
                        top_left[1] = rect_corners[3][1]
                        top_right[0] = rect_corners[2][0]   #右上角坐标
                        top_right[1] = rect_corners[2][1]
                        bottom_right[0] = rect_corners[1][0]   #右下角坐标
                        bottom_right[1] = rect_corners[1][1]
                        bottom_left[0] = rect_corners[0][0]   #左下角坐标
                        bottom_left[1] = rect_corners[0][1]

                        print("左上角的坐标是：",top_left[0],top_left[1])
                        print("右上角的坐标是：",top_right[0],top_right[1])
                        print("右下角的坐标是：",bottom_right[0],bottom_right[1])
                        print("左下角的坐标是：",bottom_left[0],bottom_left[1])

                        # 发送矩形坐标数据
                        # 数据格式: [帧头, 帧头, 中心x, 中心y, 左上角x, 左上角y, 右上角x, 右上角y, 右下角x, 右下角y, 左下角x, 左下角y]
                        data = [0x13, 0x23, center_x, center_y,
                               top_left[0], top_left[1],
                               top_right[0], top_right[1],
                               bottom_right[0], bottom_right[1],
                               bottom_left[0], bottom_left[1]]
                        serial.write(bytes(data))
                        print("已发送矩形坐标数据")
                    else:
                        print("没有找到矩形角点")

def draw_fps():
    """在屏幕上绘制FPS"""
    global fps_counter, fps_start_time, current_fps

    fps_counter += 1
    current_time = time.ticks_ms()

    # 每秒更新一次FPS显示
    if current_time - fps_start_time >= 1000:
        current_fps = fps_counter * 1000 / (current_time - fps_start_time)
        fps_counter = 0
        fps_start_time = current_time

    # 在屏幕左上角显示FPS
    fps_text = f"FPS: {current_fps:.1f}"
    img.draw_string(5, 5, fps_text, color=image.COLOR_GREEN, scale=1.5)

    disp.show(img)

# 初始化FPS计时器
fps_start_time = time.ticks_ms()

while not app.need_exit():
    img = cam.read()  # 从摄像头获取一帧图像
    juxingkuang()  # 执行矩形检测
    draw_fps()  # 绘制FPS并显示图像

