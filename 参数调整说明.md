# 简化版检测程序参数调整说明

## 🎯 主要检测阈值

### 黑色胶带检测
```python
self.black_threshold = (0, 30, -128, 127, -128, 127)
```
- **第1-2个数字 (0, 30)**: 亮度范围，调小检测更暗的区域
- **第3-4个数字 (-128, 127)**: A通道，保持不变
- **第5-6个数字 (-128, 127)**: B通道，保持不变

### 红色激光检测
```python
self.red_laser_threshold = (30, 100, 20, 127, -128, 127)
```
- **第1-2个数字 (30, 100)**: 亮度范围，激光点通常较亮
- **第3-4个数字 (20, 127)**: A通道，正值检测红色
- **第5-6个数字 (-128, 127)**: B通道，保持不变

## 📏 面积参数

### 矩形检测
```python
self.min_area = 500      # 最小面积，调大过滤小噪声
self.max_area = 8000     # 最大面积，调小过滤大背景
```

### 激光点检测
```python
self.laser_min_area = 5   # 激光点最小面积
self.laser_max_area = 100 # 激光点最大面积，防止检测光晕
```

## 🔧 常见调整场景

### 检测不到黑色胶带
1. 提高亮度上限: `(0, 40, -128, 127, -128, 127)`
2. 降低面积要求: `self.min_area = 300`

### 检测不到红色激光
1. 降低亮度下限: `(20, 100, 20, 127, -128, 127)`
2. 降低红色要求: `(30, 100, 10, 127, -128, 127)`

### 误检太多
1. 提高面积要求: `self.min_area = 800`
2. 收紧阈值范围

### 激光点太大/太小
1. 调整激光面积: `self.laser_max_area = 50` (更小)
2. 或: `self.laser_max_area = 200` (更大)

## 🎮 实时调整建议

1. **先调阈值**: 确保能检测到目标
2. **再调面积**: 过滤噪声和无关目标
3. **观察效果**: 通过屏幕显示判断效果
4. **逐步微调**: 每次只调一个参数

## 📊 性能优化

- 程序已经很简洁，FPS应该在20-30之间
- 如需更高帧率，可以降低分辨率到160x120
- 参数调整不会显著影响性能
