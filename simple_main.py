#!/usr/bin/env python3
"""
简化版激光笔和矩形检测程序
基于用户23年代码优化，简洁高效
"""

from maix import camera, display, image, app, time, uart
import math

class SimpleDetector:
    def __init__(self):
        """初始化"""
        try:
            # 首先初始化所有基本属性
            self._init_basic_attributes()

            # 然后初始化硬件
            self._init_hardware()

            print("✓ SimpleDetector初始化完成")

        except Exception as e:
            print(f"❌ SimpleDetector初始化失败: {e}")
            # 确保基本属性已设置，即使硬件初始化失败
            if not hasattr(self, 'last_sent_data'):
                self._init_basic_attributes()
            raise

    def _init_basic_attributes(self):
        """初始化基本属性"""
        # 检测阈值（用户可调）
        self.black_threshold = (0, 14, -10, 10, -11, 9)  # 黑色胶带

        # 检测参数（基于A4纸21×29.7cm + 1.8cm胶布边框）
        self.min_area = 1000       # A4纸最小面积，约168×237像素
        self.max_area = 50000       # 包含胶布边框的最大面积，约246×333像素

        # 状态变量
        self.frame_count = 0

        # 串口通信状态
        self.last_sent_data = None  # 上次发送的数据，避免重复发送
        self.send_interval = 5      # 发送间隔帧数，避免频繁发送

        # 连续检测验证
        self.stable_detection_count = 2  # 需要连续检测2次
        self.last_rectangles = []        # 存储最近的矩形检测结果
        self.coordinate_tolerance = 10   # 坐标容差（像素）

        # FPS计算
        self.fps_counter = 0
        self.fps_start_time = None  # 延迟初始化
        self.current_fps = 0.0

        # 自适应阈值调整
        self.detection_history = []  # 存储检测历史
        self.threshold_adjustment_interval = 60  # 每60帧调整一次阈值
        self.min_detection_rate = 0.3  # 最小检测率

        # 硬件对象（初始化为None）
        self.cam = None
        self.disp = None
        self.serial = None
        self.device = "/dev/ttyS0"

    def _init_hardware(self):
        """初始化硬件"""
        # 硬件初始化
        self.cam = camera.Camera(320, 240, image.Format.FMT_RGB888)
        self.disp = display.Display()

        # 串口初始化（与STM32通信）
        self.serial = uart.UART(self.device, 115200)  # 115200波特率

        # 初始化时间
        self.fps_start_time = time.ticks_ms()

    def check_attributes(self):
        """检查所有必要的属性是否存在"""
        required_attrs = [
            'last_sent_data', 'send_interval', 'frame_count',
            'stable_detection_count', 'last_rectangles', 'coordinate_tolerance',
            'fps_counter', 'current_fps', 'detection_history',
            'threshold_adjustment_interval', 'min_detection_rate'
        ]

        missing_attrs = []
        for attr in required_attrs:
            if not hasattr(self, attr):
                missing_attrs.append(attr)

        if missing_attrs:
            print(f"⚠ 缺少属性: {missing_attrs}")
            # 自动补充缺少的属性
            for attr in missing_attrs:
                if attr == 'last_sent_data':
                    setattr(self, attr, None)
                elif attr in ['send_interval', 'stable_detection_count', 'coordinate_tolerance']:
                    setattr(self, attr, 5 if attr == 'send_interval' else (2 if attr == 'stable_detection_count' else 10))
                elif attr in ['frame_count', 'fps_counter']:
                    setattr(self, attr, 0)
                elif attr in ['last_rectangles', 'detection_history']:
                    setattr(self, attr, [])
                elif attr == 'current_fps':
                    setattr(self, attr, 0.0)
                elif attr == 'threshold_adjustment_interval':
                    setattr(self, attr, 60)
                elif attr == 'min_detection_rate':
                    setattr(self, attr, 0.3)
            print(f"✓ 已自动补充缺少的属性")

        return len(missing_attrs) == 0

    def update_fps(self):
        """更新FPS"""
        self.fps_counter += 1
        current_time = time.ticks_ms()
        if current_time - self.fps_start_time >= 1000:
            self.current_fps = self.fps_counter
            self.fps_counter = 0
            self.fps_start_time = current_time

    def is_coordinate_stable(self, new_coord, coord_history):
        """检查坐标是否稳定（连续3次相同）"""
        # 添加新坐标到历史记录
        coord_history.append(new_coord)

        # 只保留最近的2次记录
        if len(coord_history) > self.stable_detection_count:
            coord_history.pop(0)

        # 检查是否有足够的历史记录
        if len(coord_history) < self.stable_detection_count:
            return False

        # 检查最近2次是否都相似
        for i in range(1, len(coord_history)):
            if not self.coordinates_similar(coord_history[0], coord_history[i]):
                return False

        return True

    def coordinates_similar(self, coord1, coord2):
        """检查两个坐标是否相似（在容差范围内）"""
        if coord1 is None or coord2 is None:
            return False

        # 比较中心点坐标 - 使用欧几里得距离
        if 'center' in coord1 and 'center' in coord2:
            dx = coord1['center'][0] - coord2['center'][0]
            dy = coord1['center'][1] - coord2['center'][1]
            distance = math.sqrt(dx * dx + dy * dy)

            # 对于矩形检测，还要考虑面积变化
            area_similar = True
            if 'area' in coord1 and 'area' in coord2:
                area_diff = abs(coord1['area'] - coord2['area']) / max(coord1['area'], coord2['area'])
                area_similar = area_diff <= 0.2  # 面积变化不超过20%

            return distance <= self.coordinate_tolerance and area_similar



        return False

    def send_to_stm32(self, data_type, x, y):
        """发送坐标数据到STM32"""
        try:
            # 构建数据包格式: START + TYPE + X + Y + EXTRA + END
            # START: 0xFF 0xFE (起始标志)
            # TYPE: 0x01=矩形中心, 0x02=激光点, 0x03=矩形+激光
            # X, Y: 2字节坐标 (小端序)
            # EXTRA: 额外数据 (可选)
            # END: 0xFD 0xFC (结束标志)

            packet = bytearray()
            packet.extend([0xFF, 0xFE])  # 起始标志

            # 数据类型 (只支持矩形)
            if data_type == "rectangle":
                packet.append(0x01)
            else:
                packet.append(0x00)  # 未知类型

            # X坐标 (2字节，小端序)
            packet.extend(x.to_bytes(2, 'little'))
            # Y坐标 (2字节，小端序)
            packet.extend(y.to_bytes(2, 'little'))

            # 矩形检测不需要额外数据

            packet.extend([0xFD, 0xFC])  # 结束标志

            # 转换为bytes类型 (MaixCam Pro API要求)
            packet_bytes = bytes(packet)

            # 发送数据
            self.serial.write(packet_bytes)

            # 打印发送的数据 (调试用)
            hex_data = ' '.join(f'{b:02X}' for b in packet_bytes)
            print(f"→ STM32: {data_type} ({x}, {y}) | 数据: {hex_data}")

            return True

        except Exception as e:
            print(f"串口发送错误: {e}")
            return False

    def should_send_data(self, current_data):
        """判断是否应该发送数据（避免重复发送）"""
        # 确保属性存在
        if not hasattr(self, 'last_sent_data'):
            self.check_attributes()

        # 检查是否与上次发送的数据相同
        if self.last_sent_data == current_data:
            return False

        # 检查发送间隔
        if self.frame_count % self.send_interval != 0:
            return False

        return True

    def calculate_rectangle_quality(self, corners):
        """计算矩形质量分数（0-100，越高越好）"""
        if not corners or len(corners) != 4:
            return 0

        # 计算四条边的长度
        sides = []
        for i in range(4):
            p1 = corners[i]
            p2 = corners[(i + 1) % 4]
            side_length = math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
            sides.append(side_length)

        # 计算对边长度比（理想矩形对边相等）
        opposite_ratio1 = min(sides[0], sides[2]) / max(sides[0], sides[2])
        opposite_ratio2 = min(sides[1], sides[3]) / max(sides[1], sides[3])

        # 计算两条对角线长度
        diag1 = math.sqrt((corners[2][0] - corners[0][0])**2 + (corners[2][1] - corners[0][1])**2)
        diag2 = math.sqrt((corners[3][0] - corners[1][0])**2 + (corners[3][1] - corners[1][1])**2)

        # 对角线长度比（理想矩形对角线相等）
        diagonal_ratio = min(diag1, diag2) / max(diag1, diag2)

        # 计算角度（检查是否接近90度）
        angles = []
        for i in range(4):
            p1 = corners[i]
            p2 = corners[(i + 1) % 4]
            p3 = corners[(i + 2) % 4]

            # 计算向量
            v1 = (p1[0] - p2[0], p1[1] - p2[1])
            v2 = (p3[0] - p2[0], p3[1] - p2[1])

            # 计算角度
            dot_product = v1[0] * v2[0] + v1[1] * v2[1]
            mag1 = math.sqrt(v1[0]**2 + v1[1]**2)
            mag2 = math.sqrt(v2[0]**2 + v2[1]**2)

            if mag1 > 0 and mag2 > 0:
                cos_angle = dot_product / (mag1 * mag2)
                cos_angle = max(-1, min(1, cos_angle))  # 限制范围
                angle = math.acos(cos_angle)
                angles.append(abs(angle - math.pi/2))  # 与90度的差值

        # 角度质量（越接近90度越好）
        angle_quality = 1.0 - (sum(angles) / len(angles)) / (math.pi/2)

        # 综合质量分数
        quality = (opposite_ratio1 * 0.3 +
                  opposite_ratio2 * 0.3 +
                  diagonal_ratio * 0.2 +
                  angle_quality * 0.2) * 100

        return max(0, min(100, quality))



    def calculate_blob_rectangle_quality(self, blob):
        """基于blob特征计算矩形质量分数"""
        # 获取blob的基本属性
        area = blob.area()
        w, h = blob.w(), blob.h()
        perimeter = blob.perimeter()

        # 1. 长宽比评估（矩形应该有合理的长宽比）
        aspect_ratio = max(w, h) / min(w, h) if min(w, h) > 0 else 0
        # 理想长宽比在1.2-2.5之间（A4纸约1.41）
        if 1.2 <= aspect_ratio <= 2.5:
            aspect_score = 1.0
        elif aspect_ratio <= 4.0:
            aspect_score = 0.7
        else:
            aspect_score = 0.3

        # 2. 填充度评估（矩形应该填充度较高）
        bbox_area = w * h
        if bbox_area > 0:
            fill_ratio = area / bbox_area
            fill_score = min(1.0, fill_ratio * 1.2)  # 稍微放宽要求
        else:
            fill_score = 0

        # 3. 紧凑度评估（基于周长和面积的关系）
        if perimeter > 0 and area > 0:
            compactness = (perimeter * perimeter) / (4 * math.pi * area)
            # 矩形的紧凑度约为1.27，圆形为1.0
            if 1.0 <= compactness <= 2.0:
                compact_score = 1.0
            elif compactness <= 3.0:
                compact_score = 0.7
            else:
                compact_score = 0.3
        else:
            compact_score = 0

        # 4. 面积合理性评估
        ideal_area = (self.min_area + self.max_area) / 2
        area_diff = abs(area - ideal_area) / ideal_area
        area_score = max(0, 1.0 - area_diff)

        # 综合质量分数
        quality = (aspect_score * 0.3 +
                  fill_score * 0.3 +
                  compact_score * 0.2 +
                  area_score * 0.2) * 100

        return max(0, min(100, quality))

    def adjust_thresholds_adaptively(self, rect_detected):
        """自适应调整检测阈值"""
        # 记录检测历史
        self.detection_history.append({
            'rect': rect_detected,
            'frame': self.frame_count
        })

        # 只保留最近的历史记录
        if len(self.detection_history) > self.threshold_adjustment_interval:
            self.detection_history.pop(0)

        # 每隔一定帧数进行阈值调整
        if self.frame_count % self.threshold_adjustment_interval == 0 and len(self.detection_history) >= 30:
            # 计算检测率
            rect_rate = sum(1 for h in self.detection_history if h['rect']) / len(self.detection_history)

            # 调整矩形检测阈值
            if rect_rate < self.min_detection_rate:
                # 检测率太低，放宽阈值
                self.min_area = max(500, self.min_area - 100)
                self.max_area = min(60000, self.max_area + 1000)
                print(f"📊 调整矩形阈值: 面积范围 {self.min_area}-{self.max_area}")
            elif rect_rate > 0.8:
                # 检测率太高，可能有误检，收紧阈值
                self.min_area = min(2000, self.min_area + 50)
                self.max_area = max(40000, self.max_area - 500)
                print(f"📊 调整矩形阈值: 面积范围 {self.min_area}-{self.max_area}")



    def find_rectangles(self, img):
        """使用find_blobs检测黑色矩形框"""
        # 使用find_blobs检测黑色区域
        black_blobs = img.find_blobs(
            [self.black_threshold],
            area_threshold=self.min_area,
            pixels_threshold=self.min_area,
            merge=True
        )

        best_rectangle = None
        best_quality = 0
        rectangles_found = 0

        for blob in black_blobs:
            if self.min_area <= blob.area() <= self.max_area:
                # 获取blob的边界框
                x, y, w, h = blob.x(), blob.y(), blob.w(), blob.h()

                # 计算矩形质量（基于blob的形状特征）
                quality = self.calculate_blob_rectangle_quality(blob)

                if quality > best_quality:
                    best_quality = quality

                    # 使用blob边界框作为矩形角点
                    corners = [
                        [x, y],           # 左上角
                        [x + w, y],       # 右上角
                        [x + w, y + h],   # 右下角
                        [x, y + h]        # 左下角
                    ]

                    best_rectangle = {
                        'corners': corners,
                        'center': (blob.cx(), blob.cy()),  # 使用blob的质心
                        'quality': quality,
                        'blob': blob
                    }

        # 处理最佳矩形
        if best_rectangle and best_quality > 30:  # 降低质量阈值
            corners = best_rectangle['corners']
            center_x, center_y = best_rectangle['center']
            quality = best_rectangle['quality']
            blob = best_rectangle['blob']

            # 根据质量选择颜色
            if quality > 70:
                corner_color = image.COLOR_BLUE
            elif quality > 50:
                corner_color = image.COLOR_GREEN
            else:
                corner_color = image.COLOR_YELLOW

            # 绘制矩形边框
            img.draw_rect(blob.x(), blob.y(), blob.w(), blob.h(),
                         color=corner_color, thickness=2)

            # 绘制四个角点
            for i, corner in enumerate(corners):
                corner_x, corner_y = corner[0], corner[1]
                img.draw_circle(corner_x, corner_y, 4, color=corner_color, thickness=-1)
                img.draw_string(corner_x+5, corner_y-5, f"{i+1}", color=corner_color, scale=0.6)

            # 绘制中心点
            img.draw_circle(center_x, center_y, 6, color=image.COLOR_RED, thickness=-1)
            img.draw_string(center_x+8, center_y-8, "C", color=image.COLOR_RED, scale=0.8)

            # 显示质量分数和面积
            img.draw_string(center_x + 15, center_y + 10, f"Q:{quality:.0f}",
                          color=image.COLOR_WHITE, scale=0.6)
            img.draw_string(center_x + 15, center_y + 25, f"A:{blob.area()}",
                          color=image.COLOR_WHITE, scale=0.6)

            # 稳定性检测
            current_rect = {
                'center': (center_x, center_y),
                'quality': quality,
                'area': blob.area()
            }

            # 检查坐标是否稳定
            if self.is_coordinate_stable(current_rect, self.last_rectangles):
                print(f"✓ 稳定矩形坐标:")
                print(f"  中心点: ({center_x}, {center_y})")
                print(f"  面积: {blob.area()}, 质量:{quality:.1f}")
                print("  [已确认稳定，可发送]")

                # 发送矩形中心坐标到STM32
                rect_data = f"rect_{center_x}_{center_y}"
                if self.should_send_data(rect_data):
                    if self.send_to_stm32("rectangle", center_x, center_y):
                        self.last_sent_data = rect_data
                        print(f"✓ 矩形中心坐标已发送到STM32")
            else:
                print(f"○ 矩形检测中... ({len(self.last_rectangles)}/2) 质量:{quality:.1f}")

            rectangles_found = 1

        return rectangles_found



    def draw_info(self, img, rect_count):
        """绘制检测信息"""
        # FPS显示
        fps_text = f"FPS: {self.current_fps:.1f}"
        img.draw_string(10, 10, fps_text, color=image.COLOR_WHITE, scale=0.8)

        # 检测状态显示
        status_text = f"Rectangles: {rect_count}"
        img.draw_string(10, 30, status_text, color=image.COLOR_GREEN, scale=0.6)

        # 参数显示
        param_text = f"Area: {self.min_area}-{self.max_area}"
        img.draw_string(10, 50, param_text, color=image.COLOR_YELLOW, scale=0.6)

        # 串口状态显示
        if self.last_sent_data:
            serial_text = f"STM32: {self.last_sent_data[:15]}..."
            img.draw_string(10, 70, serial_text, color=image.COLOR_BLUE, scale=0.6)

    def run(self):
        """主循环"""
        print("矩形检测系统启动...")
        print("检测: 黑色胶带矩形框")
        print("显示: 彩色框=矩形边界, 红点=中心")
        print("串口: /dev/ttyS0 @ 115200 -> STM32")
        print("数据格式: FF FE 01 [X坐标] [Y坐标] FD FC")
        print("类型: 01=矩形中心坐标")
        print("参数可在代码中调整")
        print("按功能键退出")

        # 检查所有属性是否正确初始化
        print("检查属性初始化...")
        if self.check_attributes():
            print("✓ 所有属性初始化正常")
        else:
            print("⚠ 部分属性已自动修复")

        while not app.need_exit():
            try:
                # 更新FPS
                self.update_fps()
                self.frame_count += 1
                
                # 读取图像
                img = self.cam.read()
                
                # 检测矩形
                rect_count = self.find_rectangles(img)

                # 自适应阈值调整
                self.adjust_thresholds_adaptively(rect_count > 0)

                # 绘制信息
                self.draw_info(img, rect_count)
                
                # 显示图像
                self.disp.show(img)
                
                # 详细的日志输出，包含坐标信息
                if self.frame_count % 60 == 0:
                    print(f"FPS: {self.current_fps:.0f}, 矩形: {rect_count}")

                    # 打印矩形信息
                    if rect_count > 0:
                        print(f"  检测到 {rect_count} 个矩形（详细坐标见上方）")
                
            except Exception as e:
                print(f"错误: {e}")
                time.sleep_ms(100)

def main():
    """主函数"""
    try:
        detector = SimpleDetector()
        detector.run()
    except KeyboardInterrupt:
        print("\n程序退出")
    except Exception as e:
        print(f"程序错误: {e}")

if __name__ == "__main__":
    main()
