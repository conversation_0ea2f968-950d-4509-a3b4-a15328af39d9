# 白色背景矩形识别配置说明

## 程序特点

这个程序专门用于检测**白色背景**上的黑色边框矩形，具有以下特点：
- ✅ 支持不同远近距离的矩形识别
- ✅ 去除了FPS显示，界面更简洁
- ✅ 调整了检测范围，适应更大的尺寸变化
- ✅ 三种检测方法确保高成功率

## 检测范围调整

### 尺寸范围（支持远近距离）
```python
# 宽度范围：8-150像素（原来20-200）
min_width_threshold = 8    # 支持更远距离的小目标
max_width_threshold = 150  # 支持更近距离的大目标

# 高度范围：6-110像素（新增）
min_height_threshold = 6   
max_height_threshold = 110

# 面积范围：50-8000像素²（新增）
min_area_threshold = 50    # 远距离小目标
max_area_threshold = 8000  # 近距离大目标
```

### 距离适应性
- **远距离**：矩形可能只有8x6像素大小
- **中距离**：矩形大小在20x15到80x60之间
- **近距离**：矩形可能达到150x110像素

## 白色背景LAB阈值

### 标准白色设置
```python
white_threshold = [[60, 100, -15, 15, -15, 15]]
```

### 不同白色背景的推荐设置

#### 1. 纯白纸张
```python
white_threshold = [[70, 100, -10, 10, -10, 10]]
```

#### 2. 米白色/象牙白
```python
white_threshold = [[60, 100, -15, 15, 5, 25]]
```

#### 3. 灰白色
```python
white_threshold = [[40, 90, -20, 20, -20, 20]]
```

#### 4. 冷白色（偏蓝）
```python
white_threshold = [[60, 100, -15, 15, -25, -5]]
```

#### 5. 暖白色（偏黄）
```python
white_threshold = [[60, 100, -15, 15, 5, 25]]
```

## 检测方法优化

### 1. 标准矩形检测（降低阈值）
```python
rects = img.find_rects(roi=roi, threshold=8000)  # 从15000降低到8000
```
- 更容易检测到远距离的小矩形
- 提高检测灵敏度

### 2. 线段检测（调整参数）
```python
lines = img.find_line_segments(roi=roi, merge_distance=3, max_theta_difference=20)
```
- `merge_distance=3`：更精确的线段合并
- `max_theta_difference=20`：允许更大的角度差异

### 3. 动态边界设置
```python
margin = max(5, min(blob_w // 10, blob_h // 10))  # 动态边界
```
- 根据目标大小自动调整ROI边界
- 小目标用小边界，大目标用大边界

## 距离检测策略

### 远距离检测优化
- **最小尺寸**：8x6像素
- **检测阈值**：降低到8000
- **边界设置**：最小5像素边界
- **像素阈值**：降低到40

### 近距离检测优化
- **最大尺寸**：150x110像素
- **面积限制**：最大8000像素²
- **边界设置**：动态调整，最大目标尺寸的10%
- **角点标记**：根据矩形大小调整标记尺寸

### 中距离检测
- **平衡设置**：在远近距离参数之间
- **自适应处理**：根据检测到的目标大小调整参数

## 使用方法

### 1. 运行测试工具
```bash
python 白色背景阈值测试.py
```

**测试工具功能**：
- 自动切换8种白色阈值预设（每6秒）
- 自动切换4种尺寸范围（每4秒）
- 显示有效/无效区域的可视化对比
- 实时显示检测统计信息

**颜色标识**：
- **绿色框**：尺寸有效的白色区域
- **黄色框**：尺寸无效的白色区域
- **红色十字**：区域中心点
- **V数字**：有效区域编号
- **X数字**：无效区域编号

### 2. 运行主程序
```bash
python 白色背景矩形识别.py
```

**主程序特点**：
- 不显示FPS，界面简洁
- 只显示检测结果（红色十字和白色角点）
- 支持三种检测方法的自动切换
- 实时串口数据发送

## 光照条件适应

### 强光环境
- L通道值增大，建议调高下限：`[70, 100, -15, 15, -15, 15]`
- 可能需要调整黑色阈值：`[0, 25, -15, 15, -15, 15]`

### 弱光环境
- L通道值减小，建议调低下限：`[45, 100, -20, 20, -20, 20]`
- 可能需要降低检测阈值：`threshold=6000`

### 人工光源
- 可能影响A/B通道，建议放宽范围：`[60, 100, -25, 25, -25, 25]`

## 性能优化

### 检测精度 vs 速度
- **高精度模式**：使用严格的尺寸和阈值设置
- **高速度模式**：放宽检测条件，减少计算量
- **平衡模式**：当前默认设置

### 内存使用优化
- 动态调整ROI大小
- 及时释放不需要的检测结果
- 合理设置blob检测的阈值

## 故障排除

### 问题1：远距离检测不到
**解决方案**：
- 降低最小尺寸阈值：`min_width_threshold = 5`
- 降低检测阈值：`threshold=6000`
- 降低像素阈值：`pixels_threshold=20`

### 问题2：近距离检测不稳定
**解决方案**：
- 增大最大尺寸限制：`max_width_threshold = 160`
- 增大面积限制：`max_area_threshold = 10000`
- 调整动态边界算法

### 问题3：白色背景检测失败
**解决方案**：
- 使用测试工具找到最佳白色阈值
- 检查光照条件是否均匀
- 调整白色阈值的L通道范围

### 问题4：黑色边框对比度不够
**解决方案**：
- 调整黑色阈值：`[0, 25, -20, 20, -20, 20]`
- 改善光照条件
- 使用更深的黑色边框材料

## 参数调优指南

### 基础参数
```python
# 根据实际使用距离调整
min_width_threshold = 8    # 最远距离能看到的最小宽度
max_width_threshold = 150  # 最近距离不超过的最大宽度

# 根据目标形状调整
min_height_threshold = 6   # 最小高度
max_height_threshold = 110 # 最大高度
```

### 检测阈值
```python
# 标准矩形检测阈值
threshold = 8000  # 降低可提高灵敏度，但可能增加误检

# blob检测阈值
pixels_threshold = 40     # 最小像素数
area_threshold = 50       # 最小面积
```

### 白色阈值微调
```python
# L通道：控制亮度范围
# A通道：控制绿-红偏向
# B通道：控制蓝-黄偏向

# 示例：更宽松的白色检测
white_threshold = [[50, 100, -20, 20, -20, 20]]
```

通过这些配置，程序能够在不同距离下稳定识别白色背景上的黑色边框矩形。
