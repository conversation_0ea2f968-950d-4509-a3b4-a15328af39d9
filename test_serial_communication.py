#!/usr/bin/env python3
"""
串口通信测试脚本
用于测试与STM32的数据传输
"""

from maix import uart, time

def test_serial_communication():
    """测试串口通信"""
    print("串口通信测试开始...")
    
    try:
        # 初始化串口
        device = "/dev/ttyS0"
        serial = uart.UART(device, 115200)
        print(f"串口 {device} 初始化成功，波特率: 115200")
        
        # 测试数据包
        test_cases = [
            # 矩形中心坐标 (160, 120)
            {
                'type': 'rectangle',
                'x': 160,
                'y': 120,
                'description': '矩形中心坐标'
            },
            # 激光点坐标 (100, 80)
            {
                'type': 'laser',
                'x': 100,
                'y': 80,
                'description': '激光点坐标'
            },
            # 组合数据：矩形(160,120) + 激光(100,80)
            {
                'type': 'both',
                'x': 160,
                'y': 120,
                'extra': {'laser_x': 100, 'laser_y': 80},
                'description': '组合数据：矩形+激光'
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            print(f"\n--- 测试 {i+1}: {test_case['description']} ---")
            
            # 构建数据包
            packet = build_packet(test_case)
            
            # 转换为bytes类型并发送数据 (MaixCam Pro API要求)
            packet_bytes = bytes(packet)
            serial.write(packet_bytes)
            
            # 打印发送的数据
            hex_data = ' '.join(f'{b:02X}' for b in packet_bytes)
            print(f"发送数据: {hex_data}")
            print(f"坐标: ({test_case['x']}, {test_case['y']})")
            
            if 'extra' in test_case:
                extra = test_case['extra']
                print(f"额外数据: 激光({extra['laser_x']}, {extra['laser_y']})")
            
            # 等待一段时间
            time.sleep_ms(1000)
        
        print("\n串口通信测试完成！")
        
    except Exception as e:
        print(f"串口测试错误: {e}")

def build_packet(test_case):
    """构建数据包"""
    packet = bytearray()
    
    # 起始标志
    packet.extend([0xFF, 0xFE])
    
    # 数据类型
    if test_case['type'] == "rectangle":
        packet.append(0x01)
    elif test_case['type'] == "laser":
        packet.append(0x02)
    elif test_case['type'] == "both":
        packet.append(0x03)
    else:
        packet.append(0x00)
    
    # X坐标 (2字节，小端序)
    packet.extend(test_case['x'].to_bytes(2, 'little'))
    # Y坐标 (2字节，小端序)
    packet.extend(test_case['y'].to_bytes(2, 'little'))
    
    # 额外数据
    if 'extra' in test_case:
        extra = test_case['extra']
        if 'laser_x' in extra and 'laser_y' in extra:
            packet.extend(extra['laser_x'].to_bytes(2, 'little'))
            packet.extend(extra['laser_y'].to_bytes(2, 'little'))
    
    # 结束标志
    packet.extend([0xFD, 0xFC])
    
    return packet

def main():
    """主函数"""
    try:
        test_serial_communication()
    except KeyboardInterrupt:
        print("\n测试中断")
    except Exception as e:
        print(f"程序错误: {e}")

if __name__ == "__main__":
    main()
