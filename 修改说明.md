# MaixCam Pro 矩形识别代码修改说明

## 修改内容概述

根据您的要求，我已经对原始代码进行了以下修改：

1. **去除激光识别功能** - 删除了所有与激光检测相关的代码
2. **保留矩形识别功能** - 保持黑色胶带边框的白色A4纸矩形检测
3. **添加宽度阈值检测** - 增加了矩形宽度过滤功能
4. **更新为MaixCam Pro API** - 使用最新的MaixPy API

## 主要修改点

### 1. 导入模块更新
```python
# 原代码
from maix import image, camera, display, app, time,uart, pinmap

# 修改后
from maix import image, camera, display, app, time, uart
```

### 2. 去除激光相关代码
- 删除了 `red_jiguang()` 函数
- 删除了激光阈值变量 `jiguang_black_thresholds`
- 删除了激光坐标变量 `x_red`, `y_red`, `flag`
- 删除了复杂的激光跟踪逻辑

### 3. 添加宽度阈值检测
```python
# 宽度阈值设置
min_width_threshold = 20   # 最小宽度阈值
max_width_threshold = 140  # 最大宽度阈值

# 在检测过程中应用宽度过滤
if blob_width < min_width_threshold or blob_width > max_width_threshold:
    continue  # 跳过不符合宽度要求的矩形
```

### 4. 简化串口数据发送
```python
# 新的数据格式：[帧头1, 帧头2, 中心x, 中心y, 左上x, 左上y, 右上x, 右上y, 右下x, 右下y, 左下x, 左下y]
data = [0x13, 0x23, center_x, center_y,
       top_left[0], top_left[1],
       top_right[0], top_right[1], 
       bottom_right[0], bottom_right[1],
       bottom_left[0], bottom_left[1]]
```

## LAB色彩空间阈值参数详解

### LAB色彩空间介绍
LAB色彩空间是一种设备无关的色彩空间，特别适合用于颜色检测：

- **L通道（亮度）**: 0-100，0为纯黑，100为纯白
- **A通道（绿-红）**: -128到127，负值偏绿色，正值偏红色
- **B通道（蓝-黄）**: -128到127，负值偏蓝色，正值偏黄色

### 黑色胶带阈值设置
```python
black_threshold = [[0, 14, -10, 10, -11, 9]]
```

参数含义：
- `[0, 14]`: L通道范围，检测很暗的区域（接近黑色）
- `[-10, 10]`: A通道范围，颜色中性（不偏红不偏绿）
- `[-11, 9]`: B通道范围，颜色中性（不偏蓝不偏黄）

### 调整建议
如果检测效果不理想，可以调整以下参数：

1. **提高检测灵敏度**（检测更多黑色区域）：
   ```python
   black_threshold = [[0, 20, -15, 15, -15, 15]]
   ```

2. **降低检测灵敏度**（只检测很黑的区域）：
   ```python
   black_threshold = [[0, 10, -5, 5, -5, 5]]
   ```

3. **调整宽度阈值**：
   ```python
   min_width_threshold = 15   # 允许更小的矩形
   max_width_threshold = 160  # 允许更大的矩形
   ```

## 使用方法

1. 将修改后的代码上传到MaixCam Pro设备
2. 确保串口连接正确（默认使用/dev/ttyS0，波特率115200）
3. 运行程序，将黑色胶带围成的白色A4纸放在摄像头前
4. 程序会自动检测矩形并通过串口发送坐标数据

## 数据格式说明

串口发送的数据格式（12字节）：
```
[0x13, 0x23, center_x, center_y, top_left_x, top_left_y, top_right_x, top_right_y, bottom_right_x, bottom_right_y, bottom_left_x, bottom_left_y]
```

- 前两个字节是帧头标识
- 接下来是中心点坐标
- 然后是四个角点的坐标（按顺时针顺序）

## 性能优化建议

1. **调整摄像头分辨率**：当前使用160x120，可根据需要调整
2. **调整检测阈值**：`threshold=11000`参数可以根据实际情况调整
3. **添加帧率控制**：如果需要稳定的帧率，可以添加延时控制

## 故障排除

1. **检测不到矩形**：
   - 检查LAB阈值设置
   - 确认光照条件
   - 调整宽度阈值范围

2. **误检测太多**：
   - 提高area_threshold和pixels_threshold
   - 缩小LAB阈值范围
   - 调整宽度阈值

3. **串口发送失败**：
   - 检查串口设备路径
   - 确认波特率设置
   - 检查串口权限
