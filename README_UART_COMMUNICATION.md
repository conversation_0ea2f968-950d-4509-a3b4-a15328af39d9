# MaixCam Pro 串口通信功能使用说明

## 🎯 功能概述

本项目为MaixCam Pro检测系统添加了与STM32的串口通信功能，可以将检测到的矩形中心坐标和激光点坐标实时发送给STM32微控制器。

## 🔧 API修正说明

### 问题
原始代码使用了错误的API调用方式：
```python
# 错误的方式
self.serial.write(bytearray_data)  # 会报错
```

### 解决方案
根据MaixCam Pro官方文档，`uart.write()` 方法需要接收 `bytes` 类型数据：
```python
# 正确的方式
packet = bytearray()
# ... 构建数据包 ...
packet_bytes = bytes(packet)  # 转换为bytes类型
self.serial.write(packet_bytes)  # 正确发送
```

## 📁 文件说明

### 主要文件
- **`simple_main.py`** - 主检测程序，包含串口通信功能
- **`simple_uart_test.py`** - 简单的串口测试脚本
- **`test_serial_communication.py`** - 完整的串口通信测试
- **`stm32_receiver_example.c`** - STM32端接收代码示例
- **`serial_protocol.md`** - 详细的通信协议文档

## 🚀 快速开始

### 1. 测试串口基本功能
```bash
python3 simple_uart_test.py
```

### 2. 运行完整检测程序
```bash
python3 simple_main.py
```

### 3. 测试串口通信协议
```bash
python3 test_serial_communication.py
```

## 📊 数据包格式

### 基本结构
```
[FF FE] [类型] [X坐标] [Y坐标] [额外数据] [FD FC]
  起始   1字节   2字节   2字节    可选     结束
```

### 数据类型
- `0x01` - 矩形中心坐标
- `0x02` - 激光点坐标  
- `0x03` - 组合数据 (矩形+激光)

### 示例数据包
```
矩形中心(160, 120): FF FE 01 A0 00 78 00 FD FC
激光点(100, 80):   FF FE 02 64 00 50 00 FD FC
```

## 🔍 关键代码片段

### 发送函数
```python
def send_to_stm32(self, data_type, x, y, extra_data=None):
    """发送坐标数据到STM32"""
    packet = bytearray()
    packet.extend([0xFF, 0xFE])  # 起始标志
    
    # 数据类型
    if data_type == "rectangle":
        packet.append(0x01)
    elif data_type == "laser":
        packet.append(0x02)
    elif data_type == "both":
        packet.append(0x03)
    
    # 坐标数据 (小端序)
    packet.extend(x.to_bytes(2, 'little'))
    packet.extend(y.to_bytes(2, 'little'))
    
    # 额外数据处理
    if extra_data and isinstance(extra_data, dict):
        if 'laser_x' in extra_data and 'laser_y' in extra_data:
            packet.extend(extra_data['laser_x'].to_bytes(2, 'little'))
            packet.extend(extra_data['laser_y'].to_bytes(2, 'little'))
    
    packet.extend([0xFD, 0xFC])  # 结束标志
    
    # 关键：转换为bytes类型
    packet_bytes = bytes(packet)
    self.serial.write(packet_bytes)
    
    return True
```

### 串口初始化
```python
# 串口初始化
self.device = "/dev/ttyS0"
self.serial = uart.UART(self.device, 115200)
```

## 🛡️ 稳定性保证

### 1. 连续检测验证
- 连续2次检测到相同坐标才发送
- 坐标容差：10像素
- 避免检测抖动导致的误发送

### 2. 发送控制
- 每5帧发送一次数据
- 避免重复发送相同数据
- 减少串口通信负载

### 3. 错误处理
- 串口发送异常捕获
- 详细的错误信息输出
- 程序不会因串口错误而崩溃

## 📱 显示信息

程序运行时会在屏幕上显示：
- 检测状态和坐标信息
- 串口发送状态
- 最后发送的数据信息
- FPS和检测参数

## 🔧 STM32端接收

使用提供的 `stm32_receiver_example.c` 代码：

1. 配置串口：115200波特率，8数据位，无校验
2. 使用状态机解析数据包
3. 根据数据类型处理不同的坐标信息

### 接收示例
```c
// 在STM32中处理接收到的数据
void HandleCoordinateData(CoordinateData_t *data) {
    switch (data->type) {
        case DATA_TYPE_RECT:
            // 处理矩形中心坐标
            printf("矩形中心: (%d, %d)\n", data->x, data->y);
            break;
        case DATA_TYPE_LASER:
            // 处理激光点坐标
            printf("激光点: (%d, %d)\n", data->x, data->y);
            break;
        case DATA_TYPE_BOTH:
            // 处理组合数据
            printf("矩形: (%d, %d), 激光: (%d, %d)\n", 
                   data->x, data->y, data->laser_x, data->laser_y);
            break;
    }
}
```

## 🐛 调试功能

### 数据包监控
程序会打印发送的每个数据包：
```
→ STM32: rectangle (160, 120) | 数据: FF FE 01 A0 00 78 00 FD FC
```

### 状态显示
- 检测稳定性状态
- 串口连接状态
- 发送频率统计

## ⚠️ 注意事项

1. **API兼容性**: 确保使用 `bytes()` 转换数据类型
2. **串口设备**: 默认使用 `/dev/ttyS0`，根据实际情况调整
3. **波特率**: 115200，确保STM32端配置一致
4. **数据格式**: 小端序编码，注意字节序
5. **错误处理**: 检查串口初始化是否成功

## 🔄 扩展功能

未来可以扩展的功能：
- 添加数据校验和
- 支持更多数据类型
- 实现双向通信
- 添加数据压缩
- 支持多个串口设备

---

现在您的MaixCam Pro可以正确地将检测到的坐标数据发送给STM32了！
