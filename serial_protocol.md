# MaixCam Pro 与 STM32 串口通信协议

## 概述
本协议定义了MaixCam Pro检测系统与STM32微控制器之间的串口通信格式，用于传输检测到的坐标数据。

## 硬件配置
- **串口设备**: `/dev/ttyS0`
- **波特率**: 115200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无

## 数据包格式

### 基本结构
```
[起始标志] [数据类型] [坐标数据] [额外数据] [结束标志]
```

### 详细格式
| 字段 | 长度(字节) | 值 | 说明 |
|------|-----------|-----|------|
| 起始标志 | 2 | 0xFF 0xFE | 数据包开始标识 |
| 数据类型 | 1 | 见下表 | 数据类型标识 |
| X坐标 | 2 | 小端序 | 主要目标X坐标 |
| Y坐标 | 2 | 小端序 | 主要目标Y坐标 |
| 额外数据 | 0-4 | 可选 | 根据数据类型而定 |
| 结束标志 | 2 | 0xFD 0xFC | 数据包结束标识 |

### 数据类型定义
| 类型值 | 含义 | 数据长度 | 说明 |
|--------|------|----------|------|
| 0x01 | 矩形中心 | 7字节 | 检测到的矩形中心坐标 |
| 0x02 | 激光点 | 7字节 | 检测到的激光点坐标 |
| 0x03 | 组合数据 | 11字节 | 矩形中心+激光点坐标 |

## 具体数据包示例

### 1. 矩形中心坐标 (160, 120)
```
FF FE 01 A0 00 78 00 FD FC
```
- `FF FE`: 起始标志
- `01`: 矩形中心类型
- `A0 00`: X坐标 160 (小端序)
- `78 00`: Y坐标 120 (小端序)
- `FD FC`: 结束标志

### 2. 激光点坐标 (100, 80)
```
FF FE 02 64 00 50 00 FD FC
```
- `FF FE`: 起始标志
- `02`: 激光点类型
- `64 00`: X坐标 100 (小端序)
- `50 00`: Y坐标 80 (小端序)
- `FD FC`: 结束标志

### 3. 组合数据：矩形(160,120) + 激光(100,80)
```
FF FE 03 A0 00 78 00 64 00 50 00 FD FC
```
- `FF FE`: 起始标志
- `03`: 组合数据类型
- `A0 00 78 00`: 矩形中心坐标 (160, 120)
- `64 00 50 00`: 激光点坐标 (100, 80)
- `FD FC`: 结束标志

## 发送策略

### 稳定性检测
- 连续2次检测到相同坐标才发送数据
- 坐标容差：10像素
- 避免因检测抖动导致的频繁发送

### 发送间隔
- 每5帧发送一次数据
- 避免重复发送相同数据
- 减少串口通信负载

### 优先级
1. **组合数据** (最高优先级)：同时检测到矩形和激光点
2. **矩形中心**：仅检测到矩形
3. **激光点**：仅检测到激光点

## STM32接收处理

### 状态机解析
```c
typedef enum {
    WAIT_START1,    // 等待第一个起始字节
    WAIT_START2,    // 等待第二个起始字节
    WAIT_TYPE,      // 等待数据类型
    WAIT_DATA,      // 等待坐标数据
    WAIT_END1,      // 等待第一个结束字节
    WAIT_END2       // 等待第二个结束字节
} ParseState_t;
```

### 数据结构
```c
typedef struct {
    uint8_t type;       // 数据类型
    uint16_t x;         // 主坐标X
    uint16_t y;         // 主坐标Y
    uint16_t laser_x;   // 激光点X (仅组合数据)
    uint16_t laser_y;   // 激光点Y (仅组合数据)
    uint8_t valid;      // 数据有效标志
} CoordinateData_t;
```

## 错误处理

### MaixCam Pro端
- 串口初始化失败时打印错误信息
- 发送失败时重试机制
- 数据包构建错误检查

### STM32端
- 起始/结束标志不匹配时重新同步
- 数据长度校验
- 超时处理机制

## 调试功能

### 数据包监控
MaixCam Pro会打印发送的数据包：
```
→ STM32: rectangle (160, 120) | 数据: FF FE 01 A0 00 78 00 FD FC
```

### 状态显示
- 屏幕上显示最后发送的数据
- 串口连接状态指示
- 发送频率统计

## 扩展性

### 未来可扩展的数据类型
- 0x04: 多个矩形
- 0x05: 轨迹数据
- 0x06: 状态信息
- 0x07-0xFF: 保留

### 协议版本控制
可在数据类型字段前添加版本号字段，实现协议向后兼容。
