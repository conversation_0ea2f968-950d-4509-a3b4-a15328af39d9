#!/usr/bin/env python3
"""
测试SimpleDetector类的初始化
检查所有属性是否正确设置
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_detector_attributes():
    """测试检测器属性初始化"""
    print("开始测试SimpleDetector属性初始化...")
    
    try:
        # 导入SimpleDetector类
        from simple_main import SimpleDetector
        
        print("✓ 成功导入SimpleDetector类")
        
        # 创建实例（可能会因为硬件初始化失败）
        try:
            detector = SimpleDetector()
            print("✓ 成功创建SimpleDetector实例")
        except Exception as e:
            print(f"⚠ 硬件初始化失败（正常）: {e}")
            # 手动创建一个简化的实例来测试属性
            detector = object.__new__(SimpleDetector)
            
            # 手动初始化关键属性
            detector.last_sent_data = None
            detector.send_interval = 5
            detector.frame_count = 0
            detector.laser_x = 0
            detector.laser_y = 0
            detector.stable_detection_count = 2
            detector.last_rectangles = []
            detector.last_laser_pos = []
            detector.coordinate_tolerance = 10
            detector.fps_counter = 0
            detector.current_fps = 0.0
            detector.detection_history = []
            detector.threshold_adjustment_interval = 60
            detector.min_detection_rate = 0.3
            
            print("✓ 手动初始化关键属性完成")
        
        # 检查关键属性
        required_attributes = [
            'last_sent_data',
            'send_interval', 
            'frame_count',
            'laser_x',
            'laser_y',
            'stable_detection_count',
            'last_rectangles',
            'last_laser_pos',
            'coordinate_tolerance',
            'fps_counter',
            'current_fps',
            'detection_history',
            'threshold_adjustment_interval',
            'min_detection_rate'
        ]
        
        print("\n检查属性:")
        for attr in required_attributes:
            if hasattr(detector, attr):
                value = getattr(detector, attr)
                print(f"✓ {attr}: {value} ({type(value).__name__})")
            else:
                print(f"✗ 缺少属性: {attr}")
        
        # 测试关键方法
        print("\n测试关键方法:")
        
        # 测试should_send_data方法
        try:
            result = detector.should_send_data("test_data")
            print(f"✓ should_send_data方法正常: {result}")
        except Exception as e:
            print(f"✗ should_send_data方法错误: {e}")
        
        print("\n属性测试完成！")
        
    except ImportError as e:
        print(f"✗ 导入错误: {e}")
    except Exception as e:
        print(f"✗ 测试错误: {e}")

def main():
    """主函数"""
    test_detector_attributes()

if __name__ == "__main__":
    main()
