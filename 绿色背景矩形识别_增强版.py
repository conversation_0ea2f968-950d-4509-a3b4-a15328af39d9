from maix import image, camera, display, app, time, uart  # 导入MaixCam Pro必要的模块

# 串口配置
device = "/dev/ttyS0"
serial = uart.UART(device, 115200)

# 摄像头和显示器初始化
cam = camera.Camera(160, 120)
disp = display.Display()

# LAB色彩空间绿色背景阈值设置
green_threshold = [[30, 80, -40, -10, -20, 20]]

# 黑色边框阈值（用于检测矩形边框）
black_threshold = [[0, 14, -10, 10, -11, 9]]

# 宽度阈值设置
min_width_threshold = 20   # 最小宽度阈值
max_width_threshold = 200  # 最大宽度阈值

# FPS计算相关变量
fps_counter = 0
fps_start_time = 0
current_fps = 0

# 连续识别成功计数器和坐标缓存
success_count = 0          # 连续成功识别次数
required_success = 2       # 需要连续成功的次数
last_detection = None      # 上次检测结果缓存
coordinate_tolerance = 5   # 坐标变化容忍度（像素）

def find_rectangle_by_lines(roi):
    """
    通过线段检测来识别矩形（适用于有突出的边框）
    """
    try:
        # 在ROI区域内检测线段
        lines = img.find_line_segments(roi=roi, merge_distance=5, max_theta_difference=15)
        
        if len(lines) >= 3:  # 至少需要3条线段才能构成近似矩形
            # 按线段长度排序，取最长的几条
            lines.sort(key=lambda line: line.length(), reverse=True)
            main_lines = lines[:4]  # 取前4条最长的线段
            
            # 计算所有线段的边界框
            min_x = min([min(line.x1(), line.x2()) for line in main_lines])
            max_x = max([max(line.x1(), line.x2()) for line in main_lines])
            min_y = min([min(line.y1(), line.y2()) for line in main_lines])
            max_y = max([max(line.y1(), line.y2()) for line in main_lines])
            
            # 构造矩形
            rect_x = min_x
            rect_y = min_y
            rect_w = max_x - min_x
            rect_h = max_y - min_y
            
            # 验证矩形的合理性
            if (rect_w > min_width_threshold and rect_w < max_width_threshold and
                rect_h > 10 and rect_h < 150 and
                rect_w > rect_h * 0.3):  # 宽高比检查
                
                # 不显示检测到的线段（注释掉）
                # for line in main_lines:
                #     img.draw_line(line.x1(), line.y1(), line.x2(), line.y2(),
                #                  color=image.COLOR_BLUE, thickness=2)
                
                # 构造伪矩形对象（用列表模拟）
                pseudo_rect = [rect_x, rect_y, rect_w, rect_h]
                
                # 构造角点
                corners = [
                    [rect_x, rect_y + rect_h],           # 左下角
                    [rect_x + rect_w, rect_y + rect_h],  # 右下角
                    [rect_x + rect_w, rect_y],           # 右上角
                    [rect_x, rect_y]                     # 左上角
                ]
                
                return [(pseudo_rect, corners)]
        
        return []
    except Exception as e:
        print(f"线段检测错误: {e}")
        return []

def find_rectangle_by_contour(roi):
    """
    通过轮廓检测来识别矩形（适用于有突出的边框）
    """
    try:
        # 在ROI区域内检测黑色区域
        black_blobs = img.find_blobs(black_threshold,
                                   roi=roi,
                                   pixels_threshold=50,
                                   area_threshold=50,
                                   merge=True)  # 合并相邻的黑色区域
        
        rectangles = []
        
        if black_blobs:
            for blob in black_blobs:
                # 检查blob的形状特征
                blob_w = blob[2]
                blob_h = blob[3]
                
                # 宽度检查
                if blob_w < min_width_threshold or blob_w > max_width_threshold:
                    continue
                
                # 形状合理性检查
                aspect_ratio = blob_w / blob_h if blob_h > 0 else 0
                if aspect_ratio < 0.3 or aspect_ratio > 10:  # 过滤极端形状
                    continue
                
                # 绘制检测到的黑色区域
                img.draw_rect(blob[0], blob[1], blob[2], blob[3], 
                             color=image.COLOR_YELLOW, thickness=1)
                
                # 构造矩形和角点
                rect_x, rect_y, rect_w, rect_h = blob[0], blob[1], blob[2], blob[3]
                
                pseudo_rect = [rect_x, rect_y, rect_w, rect_h]
                corners = [
                    [rect_x, rect_y + rect_h],           # 左下角
                    [rect_x + rect_w, rect_y + rect_h],  # 右下角
                    [rect_x + rect_w, rect_y],           # 右上角
                    [rect_x, rect_y]                     # 左上角
                ]
                
                rectangles.append((pseudo_rect, corners))
        
        return rectangles
    except Exception as e:
        print(f"轮廓检测错误: {e}")
        return []

def detect_green_rectangle_enhanced():
    """
    增强版绿色背景矩形检测
    支持有突出部分的黑色边框
    """
    # 第一步：检测绿色背景区域
    green_blobs = img.find_blobs(green_threshold, 
                                pixels_threshold=300,
                                area_threshold=300,
                                merge=True)
    
    if green_blobs:
        for green_blob in green_blobs:
            # 绿色区域宽度阈值检测
            if green_blob[2] < min_width_threshold or green_blob[2] > max_width_threshold:
                continue
            
            # 不显示绿色区域边框（注释掉）
            # img.draw_rect(green_blob[0], green_blob[1], green_blob[2], green_blob[3],
            #              color=image.COLOR_BLUE, thickness=2)
            
            # 设置ROI为绿色区域（稍微扩大一点以包含突出部分）
            margin = 10
            green_roi = [max(0, green_blob[0] - margin), 
                        max(0, green_blob[1] - margin),
                        min(160, green_blob[2] + 2*margin), 
                        min(120, green_blob[3] + 2*margin)]
            
            rectangles = []
            
            # 尝试标准矩形检测
            try:
                standard_rects = img.find_rects(roi=green_roi, threshold=12000)
                for rect in standard_rects:
                    if (rect[2] >= min_width_threshold and 
                        rect[2] <= max_width_threshold):
                        corners = rect.corners()
                        rectangles.append((rect, corners))
            except:
                pass
            
            # 如果标准检测失败，尝试线段检测
            if not rectangles:
                line_rects = find_rectangle_by_lines(green_roi)
                rectangles.extend(line_rects)
            
            # 如果线段检测也失败，尝试轮廓检测
            if not rectangles:
                contour_rects = find_rectangle_by_contour(green_roi)
                rectangles.extend(contour_rects)
            
            # 处理检测到的矩形
            for rect_data, rect_corners in rectangles:
                if isinstance(rect_data, list):
                    x3, y3, w3, h3 = rect_data
                else:
                    x3, y3, w3, h3 = rect_data[0], rect_data[1], rect_data[2], rect_data[3]
                
                # 再次检查矩形宽度
                if w3 < min_width_threshold or w3 > max_width_threshold:
                    continue
                
                # 计算并绘制矩形中心点
                center_x = x3 + w3 // 2
                center_y = y3 + h3 // 2
                img.draw_cross(center_x, center_y, color=image.COLOR_RED, size=5)
                
                print(f"增强检测-矩形中心坐标：({center_x}, {center_y})")
                print(f"矩形尺寸：宽度={w3}, 高度={h3}")
                
                # 绘制角点
                if rect_corners:
                    for corner in rect_corners:
                        img.draw_circle(corner[0], corner[1], 4, 
                                      color=image.COLOR_WHITE, thickness=2)
                    
                    # 提取四个角点坐标
                    top_left = [rect_corners[3][0], rect_corners[3][1]]
                    top_right = [rect_corners[2][0], rect_corners[2][1]]
                    bottom_right = [rect_corners[1][0], rect_corners[1][1]]
                    bottom_left = [rect_corners[0][0], rect_corners[0][1]]
                    
                    # 打印角点坐标
                    print(f"左上角：({top_left[0]}, {top_left[1]})")
                    print(f"右上角：({top_right[0]}, {top_right[1]})")
                    print(f"右下角：({bottom_right[0]}, {bottom_right[1]})")
                    print(f"左下角：({bottom_left[0]}, {bottom_left[1]})")
                    
                    # 发送矩形坐标数据
                    data = [0x13, 0x23, center_x, center_y,
                           top_left[0], top_left[1],
                           top_right[0], top_right[1],
                           bottom_right[0], bottom_right[1],
                           bottom_left[0], bottom_left[1]]
                    
                    try:
                        serial.write(bytes(data))
                        print("✓ 增强检测-矩形坐标数据发送成功")
                    except Exception as e:
                        print(f"✗ 串口发送失败: {e}")
                else:
                    print("⚠ 未找到矩形角点")

def draw_fps():
    """计算并在屏幕上显示FPS"""
    global fps_counter, fps_start_time, current_fps
    
    fps_counter += 1
    current_time = time.ticks_ms()
    
    if current_time - fps_start_time >= 1000:
        current_fps = fps_counter * 1000 / (current_time - fps_start_time)
        fps_counter = 0
        fps_start_time = current_time
    
    # 显示FPS和模式
    fps_text = f"FPS: {current_fps:.1f}"
    img.draw_string(5, 5, fps_text, color=image.COLOR_GREEN, scale=1.5)
    
    mode_text = "Enhanced"
    img.draw_string(90, 5, mode_text, color=image.COLOR_BLUE, scale=1.2)
    
    disp.show(img)

# 主程序
print("绿色背景矩形检测程序启动（增强版）")
print("支持检测有突出部分的黑色边框")
print("=" * 50)
print("检测策略：")
print("1. 标准矩形检测")
print("2. 线段检测（适用于有突出的边框）")
print("3. 轮廓检测（适用于不规则边框）")
print("=" * 50)
print(f"宽度过滤范围: {min_width_threshold}-{max_width_threshold}像素")
print("提示：将有突出部分的黑色边框矩形放在绿色背景前")
print("按Ctrl+C退出程序")

# 初始化FPS计时器
fps_start_time = time.ticks_ms()

while not app.need_exit():
    try:
        img = cam.read()
        detect_green_rectangle_enhanced()
        draw_fps()
        
    except KeyboardInterrupt:
        print("程序被用户中断")
        break
    except Exception as e:
        print(f"程序运行错误: {e}")
        continue

print("程序结束")
