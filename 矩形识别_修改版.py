from maix import image, camera, display, app, time, uart  # 导入MaixCam Pro必要的模块

# 串口配置
device = "/dev/ttyS0"
serial = uart.UART(device, 115200)

# 摄像头和显示器初始化
cam = camera.Camera(160, 120)
disp = display.Display()

# LAB色彩空间黑色边框阈值设置
# LAB色彩空间说明：
# L: 亮度通道 (0-100)，0为黑色，100为白色
# A: 绿色-红色通道 (-128到127)，负值偏绿，正值偏红
# B: 蓝色-黄色通道 (-128到127)，负值偏蓝，正值偏黄
# 
# 黑色胶带的LAB阈值：[L_min, L_max, A_min, A_max, B_min, B_max]
# [0, 14, -10, 10, -11, 9] 表示：
# - 亮度很低 (0-14)，接近黑色
# - A通道接近中性 (-10到10)，不偏红也不偏绿
# - B通道接近中性 (-11到9)，不偏蓝也不偏黄
black_threshold = [[0, 14, -10, 10, -11, 9]]

# 宽度阈值设置 - 用于过滤不符合要求的矩形
min_width_threshold = 20   # 最小宽度阈值，过滤太小的矩形
max_width_threshold = 200  # 最大宽度阈值，过滤太大的矩形

# FPS计算相关变量
fps_counter = 0
fps_start_time = 0
current_fps = 0

def detect_rectangle():
    """
    矩形检测函数
    检测黑色胶带边框的白色A4纸矩形，并发送坐标数据
    """
    rect_corners = None
    
    # 使用LAB色彩空间阈值查找黑色边框区域
    black_blobs = img.find_blobs(black_threshold, 
                                pixels_threshold=200,  # 像素数量阈值
                                area_threshold=200,    # 面积阈值
                                merge=False)           # 不合并重叠区域
    
    if black_blobs:
        for blob in black_blobs:
            # 宽度阈值检测 - 过滤不符合要求的矩形
            blob_width = blob[2]  # blob[2]是宽度
            if blob_width < min_width_threshold or blob_width > max_width_threshold:
                continue  # 跳过不符合宽度要求的矩形
            
            # 设置ROI区域，在blob周围扩展8像素
            roi = [blob[0]-8, blob[1]-8, blob[2]+16, blob[3]+16]
            
            # 限制blob尺寸范围
            if blob[2] <= 155 and blob[3] <= 115:
                # 在ROI区域内查找矩形
                rects = img.find_rects(roi=roi, threshold=15000)  # threshold参数很重要
                
                for rect in rects:
                    x3, y3, w3, h3 = rect[0], rect[1], rect[2], rect[3]
                    
                    # 再次检查矩形宽度
                    if w3 < min_width_threshold or w3 > max_width_threshold:
                        continue
                    
                    # 计算并绘制矩形中心点
                    center_x = x3 + w3 // 2
                    center_y = y3 + h3 // 2
                    img.draw_cross(center_x, center_y, color=image.COLOR_RED)
                    
                    print(f"矩形中心坐标：({center_x}, {center_y})")
                    print(f"矩形尺寸：宽度={w3}, 高度={h3}")
                    
                    # 获取矩形的四个角点
                    rect_corners = rect.corners()
                    
                    # 绘制角点
                    for corner in rect_corners:
                        img.draw_circle(corner[0], corner[1], 3, 
                                      color=image.COLOR_WHITE, thickness=1)
                    
                    if rect_corners is not None:
                        # 提取四个角点坐标
                        # 注意：角点顺序可能需要根据实际情况调整
                        top_left = [rect_corners[3][0], rect_corners[3][1]]      # 左上角
                        top_right = [rect_corners[2][0], rect_corners[2][1]]     # 右上角  
                        bottom_right = [rect_corners[1][0], rect_corners[1][1]]  # 右下角
                        bottom_left = [rect_corners[0][0], rect_corners[0][1]]   # 左下角
                        
                        # 打印角点坐标
                        print(f"左上角：({top_left[0]}, {top_left[1]})")
                        print(f"右上角：({top_right[0]}, {top_right[1]})")
                        print(f"右下角：({bottom_right[0]}, {bottom_right[1]})")
                        print(f"左下角：({bottom_left[0]}, {bottom_left[1]})")
                        
                        # 发送矩形坐标数据
                        # 数据格式：[帧头1, 帧头2, 中心x, 中心y, 左上x, 左上y, 右上x, 右上y, 右下x, 右下y, 左下x, 左下y]
                        data = [0x13, 0x23, center_x, center_y,
                               top_left[0], top_left[1],
                               top_right[0], top_right[1], 
                               bottom_right[0], bottom_right[1],
                               bottom_left[0], bottom_left[1]]
                        
                        try:
                            serial.write(bytes(data))
                            print("✓ 矩形坐标数据发送成功")
                        except Exception as e:
                            print(f"✗ 串口发送失败: {e}")
                    else:
                        print("⚠ 未找到矩形角点")

def draw_fps():
    """
    计算并在屏幕上显示FPS
    """
    global fps_counter, fps_start_time, current_fps

    fps_counter += 1
    current_time = time.ticks_ms()

    # 每秒更新一次FPS显示
    if current_time - fps_start_time >= 1000:
        current_fps = fps_counter * 1000 / (current_time - fps_start_time)
        fps_counter = 0
        fps_start_time = current_time

    # 在屏幕左上角显示FPS，使用绿色文字
    fps_text = f"FPS: {current_fps:.1f}"
    img.draw_string(5, 5, fps_text, color=image.COLOR_GREEN, scale=1.5)

    # 显示处理后的图像
    disp.show(img)

# 主循环
print("开始矩形检测...")
print("LAB阈值参数说明：")
print("L(亮度): 0-14 (黑色范围)")
print("A(绿红): -10到10 (中性)")  
print("B(蓝黄): -11到9 (中性)")
print(f"宽度过滤范围: {min_width_threshold}-{max_width_threshold}像素")
print("按Ctrl+C退出程序")

# 初始化FPS计时器
fps_start_time = time.ticks_ms()

while not app.need_exit():
    try:
        img = cam.read()     # 从摄像头获取一帧图像
        detect_rectangle()   # 执行矩形检测
        draw_fps()           # 计算并显示FPS

    except KeyboardInterrupt:
        print("程序被用户中断")
        break
    except Exception as e:
        print(f"程序运行错误: {e}")
        continue

print("程序结束")
