from maix import image, camera, display, app, time

# 摄像头和显示器初始化
cam = camera.Camera(160, 120)
disp = display.Display()

# 不同光照条件下的黑色阈值预设
LIGHTING_PRESETS = {
    1: {"name": "标准光照", "threshold": [[0, 25, -20, 20, -20, 20]]},
    2: {"name": "强光环境", "threshold": [[0, 35, -25, 25, -25, 25]]},
    3: {"name": "弱光环境", "threshold": [[0, 20, -15, 15, -15, 15]]},
    4: {"name": "室内灯光", "threshold": [[0, 30, -30, 30, -15, 25]]},
    5: {"name": "自然光", "threshold": [[0, 28, -18, 18, -22, 18]]},
    6: {"name": "宽松检测", "threshold": [[0, 40, -35, 35, -35, 35]]},
    7: {"name": "严格检测", "threshold": [[0, 18, -12, 12, -12, 12]]},
    8: {"name": "高对比度", "threshold": [[0, 15, -10, 10, -10, 10]]}
}

current_preset = 1
black_threshold = LIGHTING_PRESETS[current_preset]["threshold"]

def analyze_lighting_conditions():
    """分析当前图像的光照条件"""
    # 计算图像的平均亮度
    total_brightness = 0
    pixel_count = 0
    
    # 简单的亮度分析（采样方式）
    for y in range(0, 120, 10):
        for x in range(0, 160, 10):
            try:
                # 获取像素的LAB值（这里简化处理）
                pixel_count += 1
                # 假设亮度值，实际应该从LAB空间获取L值
                total_brightness += 50  # 占位符
            except:
                pass
    
    if pixel_count > 0:
        avg_brightness = total_brightness / pixel_count
        
        if avg_brightness > 70:
            return "强光", "可能过度曝光，建议调整光源或相机设置"
        elif avg_brightness > 50:
            return "正常", "光照条件良好"
        elif avg_brightness > 30:
            return "偏暗", "可能需要增加光照"
        else:
            return "很暗", "光照不足，建议增强照明"
    
    return "未知", "无法分析"

def detect_black_areas():
    """检测黑色区域并分析"""
    global black_threshold
    
    try:
        black_blobs = img.find_blobs(black_threshold,
                                   pixels_threshold=30,
                                   area_threshold=50,
                                   merge=True)
        
        blob_count = 0
        total_area = 0
        valid_rectangles = 0
        
        if black_blobs:
            for blob in black_blobs:
                blob_count += 1
                blob_area = blob[2] * blob[3]
                total_area += blob_area
                
                # 检查是否可能是矩形
                aspect_ratio = blob[2] / blob[3] if blob[3] > 0 else 0
                if (blob[2] >= 15 and blob[2] <= 140 and
                    blob[3] >= 12 and blob[3] <= 100 and
                    0.3 <= aspect_ratio <= 3.0):
                    
                    valid_rectangles += 1
                    # 绘制可能的矩形（绿色框）
                    img.draw_rect(blob[0], blob[1], blob[2], blob[3], 
                                 color=image.COLOR_GREEN, thickness=2)
                    
                    # 显示尺寸信息
                    size_text = f"{blob[2]}x{blob[3]}"
                    img.draw_string(blob[0], blob[1] - 10, size_text, 
                                   color=image.COLOR_GREEN, scale=0.8)
                else:
                    # 绘制其他黑色区域（黄色框）
                    img.draw_rect(blob[0], blob[1], blob[2], blob[3], 
                                 color=image.COLOR_YELLOW, thickness=1)
                
                # 绘制中心点
                center_x = blob[0] + blob[2] // 2
                center_y = blob[1] + blob[3] // 2
                img.draw_cross(center_x, center_y, color=image.COLOR_RED, size=2)
        
        return blob_count, valid_rectangles, total_area
        
    except Exception as e:
        print(f"检测错误: {e}")
        return 0, 0, 0

def draw_analysis_info():
    """绘制分析信息"""
    global current_preset
    
    # 分析光照条件
    lighting_condition, lighting_advice = analyze_lighting_conditions()
    
    # 检测黑色区域
    total_blobs, valid_rects, total_area = detect_black_areas()
    
    # 显示当前预设
    preset_info = LIGHTING_PRESETS[current_preset]
    preset_text = f"#{current_preset}: {preset_info['name']}"
    img.draw_string(5, 5, preset_text, color=image.COLOR_BLUE, scale=1.0)
    
    # 显示阈值
    threshold = preset_info['threshold'][0]
    threshold_text = f"L:{threshold[0]}-{threshold[1]} A:{threshold[2]}-{threshold[3]} B:{threshold[4]}-{threshold[5]}"
    img.draw_string(5, 18, threshold_text, color=image.COLOR_WHITE, scale=0.6)
    
    # 显示光照分析
    lighting_text = f"Light: {lighting_condition}"
    img.draw_string(5, 30, lighting_text, color=image.COLOR_WHITE, scale=0.8)
    
    # 显示检测统计
    stats_text = f"Blobs:{total_blobs} Rects:{valid_rects}"
    img.draw_string(5, 42, stats_text, color=image.COLOR_YELLOW, scale=0.8)
    
    # 显示面积信息
    area_text = f"Area:{total_area}"
    img.draw_string(5, 54, area_text, color=image.COLOR_WHITE, scale=0.7)
    
    # 显示图例
    legend = [
        "GREEN: Possible rectangles",
        "YELLOW: Other black areas",
        "RED: Center points"
    ]
    
    for i, text in enumerate(legend):
        img.draw_string(5, 70 + i*8, text, color=image.COLOR_WHITE, scale=0.5)
    
    # 显示建议
    if valid_rects == 0:
        advice_text = "No rectangles found"
        img.draw_string(5, 100, advice_text, color=image.COLOR_RED, scale=0.7)
    elif valid_rects == 1:
        advice_text = "Good! 1 rectangle detected"
        img.draw_string(5, 100, advice_text, color=image.COLOR_GREEN, scale=0.7)
    else:
        advice_text = f"Multiple rects: {valid_rects}"
        img.draw_string(5, 100, advice_text, color=image.COLOR_YELLOW, scale=0.7)
    
    disp.show(img)

def switch_preset():
    """切换光照预设"""
    global current_preset, black_threshold
    current_preset += 1
    if current_preset > len(LIGHTING_PRESETS):
        current_preset = 1
    
    black_threshold = LIGHTING_PRESETS[current_preset]["threshold"]
    print(f"切换到预设 #{current_preset}: {LIGHTING_PRESETS[current_preset]['name']}")

# 主程序
print("光照条件测试工具")
print("=" * 60)
print("用于测试不同光照条件下的黑色空心矩形检测效果")
print("=" * 60)
print("光照预设:")
for key, preset in LIGHTING_PRESETS.items():
    print(f"  {key}. {preset['name']}: {preset['threshold'][0]}")
print("=" * 60)
print("颜色标识:")
print("  绿色框: 可能的矩形区域")
print("  黄色框: 其他黑色区域")
print("  红色十字: 区域中心点")
print("=" * 60)
print("光照建议:")
print("1. 确保均匀照明，避免强烈阴影")
print("2. 黑色边框与白色背景要有足够对比度")
print("3. 避免反光和过度曝光")
print("4. 观察哪个预设效果最好")
print("=" * 60)
print("程序将每5秒自动切换预设")
print("按Ctrl+C退出程序")

# 初始化
switch_time = time.ticks_ms()
SWITCH_INTERVAL = 5000  # 5秒切换

print(f"当前预设: #{current_preset} - {LIGHTING_PRESETS[current_preset]['name']}")

while not app.need_exit():
    try:
        img = cam.read()
        
        # 自动切换预设
        current_time = time.ticks_ms()
        if current_time - switch_time > SWITCH_INTERVAL:
            switch_preset()
            switch_time = current_time
        
        draw_analysis_info()
        
    except KeyboardInterrupt:
        print("程序被用户中断")
        break
    except Exception as e:
        print(f"程序运行错误: {e}")
        continue

print("测试结束")
print(f"最后使用的预设: #{current_preset} - {LIGHTING_PRESETS[current_preset]['name']}")
print("请将最佳预设应用到主程序中")
