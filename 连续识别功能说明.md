# 连续识别功能说明

## 功能概述

为了提高矩形检测的稳定性和准确性，程序现在要求**连续识别成功两次**后才传输坐标数据。这样可以有效减少误检测和不稳定的检测结果。

## 工作原理

### 1. 连续识别机制
```python
success_count = 0          # 连续成功识别次数
required_success = 2       # 需要连续成功的次数
last_detection = None      # 上次检测结果缓存
coordinate_tolerance = 5   # 坐标变化容忍度（像素）
```

### 2. 稳定性检查
程序会比较当前检测结果与上次检测结果：
- **中心点坐标**：X和Y坐标变化不超过5像素
- **矩形尺寸**：宽度和高度变化不超过5像素

### 3. 计数逻辑
```
第1次检测成功 → success_count = 1 → 等待下次检测
第2次检测成功且稳定 → success_count = 2 → 发送坐标数据
检测失败或不稳定 → success_count = 0 → 重新开始计数
```

## 检测流程

### 完整检测流程
```
1. 检测矩形
   ↓
2. 检测成功？
   ├─ 否 → 重置计数器 → 返回步骤1
   └─ 是 → 继续步骤3
   ↓
3. 与上次结果比较
   ├─ 第一次检测 → success_count = 1
   ├─ 坐标稳定 → success_count += 1
   └─ 坐标变化大 → success_count = 1（重新计数）
   ↓
4. 检查计数器
   ├─ success_count < 2 → 等待下次检测
   └─ success_count >= 2 → 发送坐标数据
```

### 稳定性判断标准
```python
def is_detection_stable(current_detection):
    # 中心点坐标差异
    center_diff_x = abs(current_detection['center_x'] - last_detection['center_x'])
    center_diff_y = abs(current_detection['center_y'] - last_detection['center_y'])
    
    # 矩形尺寸差异
    size_diff_w = abs(current_detection['width'] - last_detection['width'])
    size_diff_h = abs(current_detection['height'] - last_detection['height'])
    
    # 所有差异都在容忍范围内才算稳定
    return (center_diff_x <= 5 and center_diff_y <= 5 and
            size_diff_w <= 5 and size_diff_h <= 5)
```

## 优势分析

### 1. 减少误检测
- **问题**：单次检测可能受到光照变化、阴影、反射等干扰
- **解决**：连续两次稳定检测确保结果可靠

### 2. 提高精度
- **问题**：检测结果可能在几个像素范围内跳动
- **解决**：坐标容忍度机制过滤小幅波动

### 3. 增强稳定性
- **问题**：移动目标或摄像头抖动导致检测不稳定
- **解决**：只有连续稳定的检测才会触发数据传输

### 4. 减少通信负担
- **问题**：频繁发送不稳定的坐标数据
- **解决**：只发送经过验证的稳定坐标

## 参数调整

### 1. 连续成功次数
```python
required_success = 2  # 可调整为1-5次
```
- **1次**：实时性最高，稳定性最低
- **2次**：平衡实时性和稳定性（推荐）
- **3-5次**：稳定性最高，但响应较慢

### 2. 坐标容忍度
```python
coordinate_tolerance = 5  # 可调整为1-10像素
```
- **1-3像素**：严格模式，要求高精度
- **5像素**：标准模式，平衡精度和稳定性（推荐）
- **8-10像素**：宽松模式，适用于抖动环境

## 使用场景

### 适用场景
- ✅ 需要高精度坐标的应用
- ✅ 目标相对静止或缓慢移动
- ✅ 对误检测敏感的系统
- ✅ 通信带宽有限的环境

### 不适用场景
- ❌ 需要极高实时性的应用
- ❌ 目标快速移动的场景
- ❌ 单次检测精度已足够的简单应用

## 调试信息

### 控制台输出示例
```
✓ 使用标准矩形检测
白色背景矩形中心坐标：(80, 60)
矩形尺寸：宽度=45, 高度=32, 面积=1440
连续识别成功次数: 1/2
等待连续识别成功 2 次后发送坐标...

✓ 使用标准矩形检测  
白色背景矩形中心坐标：(82, 61)
矩形尺寸：宽度=46, 高度=33, 面积=1518
连续识别成功次数: 2/2
✓ 连续识别成功，矩形坐标数据发送成功
发送数据: 中心(82,61) 角点已发送
```

### 状态说明
- **"连续识别成功次数: X/2"**：当前计数状态
- **"等待连续识别成功 2 次后发送坐标..."**：还需要更多次成功检测
- **"检测结果变化，重新计数: 1/2"**：坐标变化超出容忍度，重新开始计数
- **"未检测到矩形，重置计数器"**：检测失败，清空计数

## 性能影响

### 延迟分析
- **额外延迟**：1-2帧的延迟（取决于帧率）
- **30FPS时**：约33-66毫秒额外延迟
- **15FPS时**：约67-133毫秒额外延迟

### 计算开销
- **内存**：增加少量内存存储上次检测结果
- **CPU**：增加坐标比较计算，开销很小
- **总体影响**：几乎可以忽略

## 故障排除

### 问题1：长时间不发送坐标
**可能原因**：
- 检测结果不稳定，坐标一直在变化
- 容忍度设置过严格

**解决方案**：
- 增大坐标容忍度：`coordinate_tolerance = 8`
- 改善检测环境（光照、稳定性）
- 临时降低要求次数：`required_success = 1`

### 问题2：响应太慢
**可能原因**：
- 要求的连续成功次数过多
- 检测环境不稳定

**解决方案**：
- 减少要求次数：`required_success = 1`
- 优化检测环境
- 提高摄像头帧率

### 问题3：仍有误检测
**可能原因**：
- 容忍度设置过宽松
- 连续成功次数不够

**解决方案**：
- 减小容忍度：`coordinate_tolerance = 3`
- 增加要求次数：`required_success = 3`
- 优化检测算法参数

## 自定义配置

### 高精度模式
```python
required_success = 3       # 要求连续3次成功
coordinate_tolerance = 3   # 严格的坐标容忍度
```

### 快速响应模式
```python
required_success = 1       # 单次检测即发送
coordinate_tolerance = 8   # 宽松的坐标容忍度
```

### 超稳定模式
```python
required_success = 5       # 要求连续5次成功
coordinate_tolerance = 2   # 非常严格的坐标容忍度
```

通过这个连续识别机制，程序的检测稳定性和准确性得到了显著提升，特别适合对精度要求较高的应用场景。
