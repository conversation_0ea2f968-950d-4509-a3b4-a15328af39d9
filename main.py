#!/usr/bin/env python3
"""
MaixCAM Pro 黑色胶带边框识别程序（优化版）
功能：识别摄像头中用约1.8cm宽的黑色电工胶带沿A4纸四边贴成的边框
说明：检测空心矩形边框，中间是白色A4纸，四周是黑色胶带线条
特性：实时帧率显示、边框检测算法、性能优化、降低CPU占用
作者：AI Assistant
"""

from maix import camera, display, image, app, time
import cv2
import numpy as np

class RectangleDetector:
    def __init__(self):
        """初始化矩形检测器"""
        # 初始化摄像头和显示器（降低分辨率提升性能）
        self.cam = camera.Camera(320, 240, image.Format.FMT_RGB888)
        self.disp = display.Display()

        # 黑色胶带的HSV阈值范围（可根据实际情况调整）
        self.lower_black = np.array([0, 0, 0])      # 黑色下限
        self.upper_black = np.array([180, 255, 50]) # 黑色上限

        # 矩形检测参数（针对边框检测调整）
        self.min_area = 500        # 最小面积阈值（边框需要一定大小）
        self.max_area = 5000       # 最大面积阈值
        self.min_aspect_ratio = 0.5 # 最小宽高比（接近A4纸比例）
        self.max_aspect_ratio = 2.5 # 最大宽高比（接近A4纸比例）
        self.approx_epsilon = 0.015 # 轮廓近似精度（更精确）

        # 性能优化参数
        self.process_every_n_frames = 3  # 每N帧处理一次检测
        self.frame_count = 0
        self.last_rectangles = []        # 缓存上次检测结果

        # 帧率计算相关
        self.fps_counter = 0
        self.fps_start_time = time.ticks_ms()
        self.current_fps = 0.0
        self.fps_update_interval = 1000  # 每1秒更新一次FPS显示

        # 预分配内存优化
        self.kernel = np.ones((3, 3), np.uint8)

        # 调试模式
        self.debug_mode = True  # 设置为True显示处理过程
        self.show_binary = False  # 是否显示二值化图像

        # 胶带参数
        self.tape_width_cm = 1.8  # 胶带宽度（厘米）
        self.estimated_tape_width_pixels = 12  # 在当前分辨率下的胶带宽度（像素）

    def update_fps(self):
        """更新帧率计算"""
        self.fps_counter += 1
        current_time = time.ticks_ms()

        if current_time - self.fps_start_time >= self.fps_update_interval:
            # 计算FPS
            elapsed_time = (current_time - self.fps_start_time) / 1000.0
            self.current_fps = self.fps_counter / elapsed_time

            # 重置计数器
            self.fps_counter = 0
            self.fps_start_time = current_time

    def preprocess_image(self, img_cv):
        """
        图像预处理（针对反光胶带优化）
        Args:
            img_cv: OpenCV格式的图像
        Returns:
            binary: 二值化后的图像
        """
        # 转换为灰度图像
        gray = cv2.cvtColor(img_cv, cv2.COLOR_RGB2GRAY)

        # 方法1: 边缘检测 - 对反光胶带更有效
        # 使用Canny边缘检测来检测胶带边缘
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)

        # 膨胀操作连接断开的边缘
        kernel_dilate = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        edges = cv2.dilate(edges, kernel_dilate, iterations=2)

        # 方法2: 多阈值组合 - 处理反光问题
        # 低阈值捕获暗区域
        _, thresh_low = cv2.threshold(gray, 60, 255, cv2.THRESH_BINARY_INV)

        # 高阈值捕获非常暗的区域
        _, thresh_high = cv2.threshold(gray, 30, 255, cv2.THRESH_BINARY_INV)

        # 自适应阈值处理局部光照变化
        adaptive = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                       cv2.THRESH_BINARY_INV, 15, 8)

        # 组合多种方法的结果
        combined = cv2.bitwise_or(edges, thresh_low)
        combined = cv2.bitwise_or(combined, thresh_high)
        combined = cv2.bitwise_or(combined, adaptive)

        # 形态学操作优化
        # 闭运算连接断开的线条
        kernel_close = cv2.getStructuringElement(cv2.MORPH_RECT, (7, 7))
        combined = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel_close)

        # 开运算去除噪声
        kernel_open = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        combined = cv2.morphologyEx(combined, cv2.MORPH_OPEN, kernel_open)

        return combined

    def find_rectangles(self, binary_img):
        """
        在二值图像中查找矩形边框（空心矩形）
        Args:
            binary_img: 二值化图像
        Returns:
            rectangles: 检测到的矩形列表
        """
        rectangles = []

        # 查找轮廓 - 使用RETR_TREE来获取层次结构
        contours, hierarchy = cv2.findContours(binary_img, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

        if hierarchy is None:
            return rectangles

        for i, contour in enumerate(contours):
            # 计算轮廓面积
            area = cv2.contourArea(contour)

            # 过滤太小的轮廓（调整阈值适应边框检测）
            if area < self.min_area:
                continue

            # 轮廓近似
            perimeter = cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, self.approx_epsilon * perimeter, True)

            # 检查是否为四边形
            if len(approx) == 4:
                # 计算边界矩形
                x, y, w, h = cv2.boundingRect(approx)
                aspect_ratio = float(w) / h

                # 检查宽高比是否合理（A4纸比例约为1.414）
                if self.min_aspect_ratio <= aspect_ratio <= self.max_aspect_ratio:

                    # 检查是否为外轮廓（没有父轮廓）或者是包含内部轮廓的外轮廓
                    is_outer_contour = hierarchy[0][i][3] == -1  # 没有父轮廓

                    # 额外检查：验证这是一个边框而不是实心区域
                    # 通过检查轮廓内部是否主要是白色来判断
                    if self.is_frame_rectangle(binary_img, approx, x, y, w, h):
                        rectangles.append({
                            'contour': approx,
                            'area': area,
                            'bbox': (x, y, w, h),
                            'aspect_ratio': aspect_ratio,
                            'is_outer': is_outer_contour
                        })

        return rectangles

    def is_frame_rectangle(self, binary_img, contour, x, y, w, h):
        """
        判断是否为边框矩形（针对反光胶带优化）
        Args:
            binary_img: 二值化图像
            contour: 轮廓
            x, y, w, h: 边界矩形
        Returns:
            bool: 是否为边框矩形
        """
        # 检查轮廓是否足够大
        if w < 50 or h < 50:
            return False

        # 检查轮廓的周长与面积比例（边框应该有较大的周长面积比）
        perimeter = cv2.arcLength(contour, True)
        area = cv2.contourArea(contour)
        if area == 0:
            return False

        # 计算紧密度（周长²/面积），矩形的理论值约为16
        compactness = (perimeter * perimeter) / area

        # 边框的紧密度应该在合理范围内
        if compactness < 12 or compactness > 50:
            return False

        # 检查轮廓的凸性（矩形应该接近凸形）
        hull = cv2.convexHull(contour)
        hull_area = cv2.contourArea(hull)
        if hull_area == 0:
            return False

        solidity = area / hull_area
        if solidity < 0.8:  # 凸性不够
            return False

        # 简化的内部检查 - 检查中心区域是否相对较亮
        center_x, center_y = x + w//2, y + h//2
        center_region_size = min(w//4, h//4, 20)

        if (center_y - center_region_size >= 0 and center_y + center_region_size < binary_img.shape[0] and
            center_x - center_region_size >= 0 and center_x + center_region_size < binary_img.shape[1]):

            center_region = binary_img[center_y-center_region_size:center_y+center_region_size,
                                     center_x-center_region_size:center_x+center_region_size]

            if center_region.size > 0:
                center_white_ratio = np.sum(center_region == 0) / center_region.size
                # 中心区域应该主要是白色（在二值图像中为0）
                if center_white_ratio < 0.3:
                    return False

        return True

    def draw_rectangles(self, img_cv, rectangles):
        """
        在图像上绘制检测到的矩形边框（在胶带中心线上）
        Args:
            img_cv: OpenCV格式的图像
            rectangles: 检测到的矩形列表
        Returns:
            img_cv: 绘制了矩形的图像
        """
        for i, rect in enumerate(rectangles):
            # 获取轮廓的四个角点
            contour = rect['contour']

            # 计算胶带的中心线矩形
            tape_center_rect = self.calculate_tape_center_rectangle(contour)

            if tape_center_rect is not None:
                # 用蓝色线条绘制胶带中心线矩形
                cv2.drawContours(img_cv, [tape_center_rect], -1, (0, 0, 255), 3)
            else:
                # 如果无法计算中心线，则绘制原始轮廓
                cv2.drawContours(img_cv, [contour], -1, (0, 0, 255), 3)

            # 在矩形上方显示信息
            x, y, w, h = rect['bbox']
            info_text = f"Frame {i+1}"
            cv2.putText(img_cv, info_text, (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)

            # 显示矩形的宽高比（帮助判断是否为A4纸）
            ratio_text = f"{rect['aspect_ratio']:.2f}"
            cv2.putText(img_cv, ratio_text, (x, y+h+15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 0), 1)

        return img_cv

    def calculate_tape_center_rectangle(self, contour):
        """
        计算胶带中心线形成的矩形（精确版本）
        Args:
            contour: 检测到的轮廓（包含胶带边缘）
        Returns:
            center_rect: 胶带中心线矩形的轮廓点
        """
        try:
            # 方法1: 使用最小外接矩形计算中心
            rect = cv2.minAreaRect(contour)
            center_x, center_y = rect[0]
            width, height = rect[1]
            angle = rect[2]

            # 计算胶带中心线矩形
            # 向内缩进胶带宽度的一半
            shrink_amount = self.estimated_tape_width_pixels / 2

            # 确保缩进后仍有合理的尺寸
            inner_width = max(width - 2 * shrink_amount, width * 0.7)
            inner_height = max(height - 2 * shrink_amount, height * 0.7)

            # 创建中心矩形
            center_rect = ((center_x, center_y), (inner_width, inner_height), angle)
            center_box = cv2.boxPoints(center_rect)
            center_box = np.int0(center_box)

            return center_box.reshape((-1, 1, 2))

        except Exception as e:
            if self.debug_mode:
                print(f"计算胶带中心矩形时出错: {e}")
            return None

    def draw_tape_center_lines(self, img_cv, contour):
        """
        绘制胶带中心线（备用方法）
        Args:
            img_cv: OpenCV格式的图像
            contour: 轮廓
        """
        try:
            # 获取轮廓的四个角点
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, True)

            if len(approx) == 4:
                # 计算每条边的中点
                points = approx.reshape(4, 2)

                # 计算对边的中点连线
                mid_points = []
                for i in range(4):
                    next_i = (i + 1) % 4
                    mid_x = (points[i][0] + points[next_i][0]) // 2
                    mid_y = (points[i][1] + points[next_i][1]) // 2
                    mid_points.append([mid_x, mid_y])

                # 绘制中心矩形
                center_contour = np.array(mid_points).reshape((-1, 1, 2))
                cv2.drawContours(img_cv, [center_contour], -1, (0, 0, 255), 2)

        except Exception as e:
            if self.debug_mode:
                print(f"绘制胶带中心线时出错: {e}")

    def draw_fps_info(self, img_cv, rectangles_count):
        """
        绘制帧率和检测信息
        Args:
            img_cv: OpenCV格式的图像
            rectangles_count: 检测到的矩形数量
        """
        # 显示FPS
        fps_text = f"FPS: {self.current_fps:.1f}"
        cv2.putText(img_cv, fps_text, (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

        # 显示检测到的矩形数量
        rect_text = f"Frames: {rectangles_count}"
        cv2.putText(img_cv, rect_text, (10, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

        # 显示分辨率信息
        res_text = f"Res: {img_cv.shape[1]}x{img_cv.shape[0]}"
        cv2.putText(img_cv, res_text, (10, 75), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 1)

        # 显示检测提示
        if rectangles_count == 0:
            tip_text = "No frame detected - Check lighting"
            cv2.putText(img_cv, tip_text, (10, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

        # 显示调试信息
        if self.debug_mode:
            debug_text = "Tape center mode ON"
            cv2.putText(img_cv, debug_text, (10, 125), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)

        # 显示胶带中心线说明
        if rectangles_count > 0:
            center_text = "Blue = Tape center line"
            cv2.putText(img_cv, center_text, (10, 150), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 255), 1)

    def run(self):
        """主运行循环（优化版）"""
        print("开始黑色胶带边框识别（胶带中心线版）...")
        print("检测目标: A4纸四周的黑色胶带边框（中间白色）")
        print("显示效果: 蓝色方框显示在胶带中心线上")
        print("特殊优化: 处理胶带反光问题")
        print("分辨率: 320x240 (优化性能)")
        print("算法: 多阈值+边缘检测+中心线计算")
        print("按设备上的功能键退出程序")

        while not app.need_exit():
            try:
                # 更新帧率计算
                self.update_fps()

                # 读取摄像头图像
                img = self.cam.read()

                # 转换为OpenCV格式（优化：减少内存拷贝）
                img_cv = image.image2cv(img, ensure_bgr=False, copy=False)

                # 性能优化：不是每帧都进行检测
                self.frame_count += 1
                if self.frame_count % self.process_every_n_frames == 0:
                    # 图像预处理
                    binary_img = self.preprocess_image(img_cv)

                    # 查找矩形
                    self.last_rectangles = self.find_rectangles(binary_img)

                # 调试模式：显示二值化图像
                if self.show_binary:
                    # 将二值图像转换为3通道以便显示
                    binary_display = cv2.cvtColor(binary_img, cv2.COLOR_GRAY2RGB)
                    display_img = image.cv2image(binary_display, bgr=False, copy=False)
                    self.disp.show(display_img)
                    continue

                # 绘制检测结果（使用缓存的结果）
                result_img = self.draw_rectangles(img_cv.copy(), self.last_rectangles)

                # 绘制帧率和检测信息
                self.draw_fps_info(result_img, len(self.last_rectangles))

                # 转换回maix格式并显示
                display_img = image.cv2image(result_img, bgr=False, copy=False)
                self.disp.show(display_img)

                # 减少控制台输出以提升性能
                if self.last_rectangles and self.frame_count % 30 == 0:  # 每30帧打印一次
                    print(f"FPS: {self.current_fps:.1f}, 检测到 {len(self.last_rectangles)} 个矩形")

                # 移除延时以获得最大帧率
                # time.sleep_ms(50)  # 注释掉延时

            except Exception as e:
                print(f"处理图像时出错: {e}")
                time.sleep_ms(100)

def main():
    """主函数"""
    try:
        detector = RectangleDetector()
        detector.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
    finally:
        print("程序结束")

if __name__ == "__main__":
    main()