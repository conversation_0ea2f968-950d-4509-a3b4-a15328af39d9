from maix import image, camera, display, app, time

# 摄像头和显示器初始化
cam = camera.Camera(160, 120)
disp = display.Display()

# 检测参数
green_threshold = [[30, 80, -40, -10, -20, 20]]
black_threshold = [[0, 14, -10, 10, -11, 9]]

# 检测模式
detection_modes = {
    1: "标准矩形检测",
    2: "线段检测",
    3: "轮廓检测",
    4: "组合检测"
}

current_mode = 1
fps_counter = 0
fps_start_time = 0
current_fps = 0

def detect_by_standard_rects(roi):
    """标准矩形检测"""
    try:
        rects = img.find_rects(roi=roi, threshold=12000)
        results = []
        
        if rects:
            for rect in rects:
                x, y, w, h = rect[0], rect[1], rect[2], rect[3]
                if w >= 20 and w <= 200 and h >= 10 and h <= 150:
                    # 绘制矩形
                    img.draw_rect(x, y, w, h, color=image.COLOR_RED, thickness=2)
                    results.append((x, y, w, h, "RECT"))
        
        return results
    except Exception as e:
        print(f"标准检测错误: {e}")
        return []

def detect_by_lines(roi):
    """线段检测方法"""
    try:
        lines = img.find_line_segments(roi=roi, merge_distance=5, max_theta_difference=15)
        results = []
        
        if len(lines) >= 3:
            # 绘制所有检测到的线段
            for line in lines:
                img.draw_line(line.x1(), line.y1(), line.x2(), line.y2(), 
                             color=image.COLOR_BLUE, thickness=2)
            
            # 按长度排序，取最长的几条
            lines.sort(key=lambda line: line.length(), reverse=True)
            main_lines = lines[:4]
            
            if main_lines:
                # 计算边界框
                min_x = min([min(line.x1(), line.x2()) for line in main_lines])
                max_x = max([max(line.x1(), line.x2()) for line in main_lines])
                min_y = min([min(line.y1(), line.y2()) for line in main_lines])
                max_y = max([max(line.y1(), line.y2()) for line in main_lines])
                
                w = max_x - min_x
                h = max_y - min_y
                
                if w >= 20 and w <= 200 and h >= 10 and h <= 150:
                    # 绘制边界框
                    img.draw_rect(min_x, min_y, w, h, color=image.COLOR_YELLOW, thickness=2)
                    results.append((min_x, min_y, w, h, "LINES"))
        
        return results
    except Exception as e:
        print(f"线段检测错误: {e}")
        return []

def detect_by_blobs(roi):
    """轮廓/blob检测方法"""
    try:
        black_blobs = img.find_blobs(black_threshold,
                                   roi=roi,
                                   pixels_threshold=50,
                                   area_threshold=50,
                                   merge=True)
        results = []
        
        if black_blobs:
            for blob in black_blobs:
                x, y, w, h = blob[0], blob[1], blob[2], blob[3]
                if w >= 20 and w <= 200 and h >= 10 and h <= 150:
                    # 绘制blob
                    img.draw_rect(x, y, w, h, color=image.COLOR_GREEN, thickness=2)
                    results.append((x, y, w, h, "BLOB"))
        
        return results
    except Exception as e:
        print(f"轮廓检测错误: {e}")
        return []

def detect_combined(roi):
    """组合检测方法"""
    all_results = []
    
    # 尝试所有方法
    all_results.extend(detect_by_standard_rects(roi))
    all_results.extend(detect_by_lines(roi))
    all_results.extend(detect_by_blobs(roi))
    
    return all_results

def test_detection():
    """测试不同的检测方法"""
    # 检测绿色区域
    green_blobs = img.find_blobs(green_threshold,
                                pixels_threshold=200,
                                area_threshold=200,
                                merge=True)
    
    detection_count = 0
    
    if green_blobs:
        for green_blob in green_blobs:
            # 绘制绿色区域
            img.draw_rect(green_blob[0], green_blob[1], green_blob[2], green_blob[3],
                         color=image.COLOR_WHITE, thickness=1)
            
            # 设置ROI
            margin = 10
            roi = [max(0, green_blob[0] - margin),
                  max(0, green_blob[1] - margin),
                  min(160, green_blob[2] + 2*margin),
                  min(120, green_blob[3] + 2*margin)]
            
            # 根据当前模式进行检测
            results = []
            if current_mode == 1:
                results = detect_by_standard_rects(roi)
            elif current_mode == 2:
                results = detect_by_lines(roi)
            elif current_mode == 3:
                results = detect_by_blobs(roi)
            elif current_mode == 4:
                results = detect_combined(roi)
            
            detection_count += len(results)
            
            # 显示检测结果
            for i, (x, y, w, h, method) in enumerate(results):
                center_x = x + w // 2
                center_y = y + h // 2
                img.draw_cross(center_x, center_y, color=image.COLOR_RED, size=3)
                
                # 显示方法标识
                img.draw_string(x + 2, y + 2, method[:4], 
                               color=image.COLOR_WHITE, scale=0.8)
    
    return detection_count

def draw_info():
    """绘制信息显示"""
    global fps_counter, fps_start_time, current_fps
    
    # 计算FPS
    fps_counter += 1
    current_time = time.ticks_ms()
    
    if current_time - fps_start_time >= 1000:
        current_fps = fps_counter * 1000 / (current_time - fps_start_time)
        fps_counter = 0
        fps_start_time = current_time
    
    # 执行检测
    count = test_detection()
    
    # 显示信息
    fps_text = f"FPS: {current_fps:.1f}"
    img.draw_string(5, 5, fps_text, color=image.COLOR_GREEN, scale=1.2)
    
    mode_text = f"Mode {current_mode}"
    img.draw_string(80, 5, mode_text, color=image.COLOR_BLUE, scale=1.0)
    
    method_text = detection_modes[current_mode]
    img.draw_string(5, 20, method_text, color=image.COLOR_WHITE, scale=0.8)
    
    count_text = f"Found: {count}"
    img.draw_string(5, 35, count_text, color=image.COLOR_YELLOW, scale=1.0)
    
    # 显示颜色说明
    legend = [
        "RED: Standard Rect",
        "BLUE: Line Segments", 
        "GREEN: Blob Contour",
        "YELLOW: Line Boundary"
    ]
    
    for i, text in enumerate(legend):
        img.draw_string(5, 50 + i*10, text, color=image.COLOR_WHITE, scale=0.6)
    
    help_text = "Auto switch every 8s"
    img.draw_string(5, 105, help_text, color=image.COLOR_WHITE, scale=0.7)
    
    disp.show(img)

def switch_mode():
    """切换检测模式"""
    global current_mode
    current_mode += 1
    if current_mode > len(detection_modes):
        current_mode = 1
    
    print(f"切换到模式 {current_mode}: {detection_modes[current_mode]}")

# 主程序
print("突出边框检测测试工具")
print("=" * 50)
print("测试不同的检测方法对突出边框的识别效果")
print("=" * 50)
print("检测方法说明:")
for mode, desc in detection_modes.items():
    print(f"  {mode}. {desc}")
print("=" * 50)
print("颜色标识:")
print("  红色框: 标准矩形检测")
print("  蓝色线: 线段检测")
print("  绿色框: 轮廓检测")
print("  黄色框: 线段边界框")
print("  白色框: 绿色区域")
print("=" * 50)
print("程序将每8秒自动切换检测模式")
print("观察哪种方法最适合您的突出边框")
print("按Ctrl+C退出程序")

# 初始化
fps_start_time = time.ticks_ms()
mode_switch_time = time.ticks_ms()
MODE_SWITCH_INTERVAL = 8000  # 8秒切换

print(f"当前模式: {current_mode} - {detection_modes[current_mode]}")

while not app.need_exit():
    try:
        img = cam.read()
        
        # 自动切换模式
        current_time = time.ticks_ms()
        if current_time - mode_switch_time > MODE_SWITCH_INTERVAL:
            switch_mode()
            mode_switch_time = current_time
        
        draw_info()
        
    except KeyboardInterrupt:
        print("程序被用户中断")
        break
    except Exception as e:
        print(f"程序运行错误: {e}")
        continue

print("测试结束")
print(f"最后使用的模式: {current_mode} - {detection_modes[current_mode]}")
print("请根据测试结果选择最适合的检测方法")
